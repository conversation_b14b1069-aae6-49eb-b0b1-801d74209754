#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run the stock data job with specific tickers for testing.
"""

import os
import sys
from dotenv import load_dotenv

from utils.logging import setup_logging

# Add the current directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from jobs.stock_data_job import StockDataJob

def main():
    """
    Main entry point.
    """
    # Load environment variables
    load_dotenv()
    
    # Check if MySQL environment variables are set
    # required_env_vars = [
    #     'MYSQL_HOST', 'MYSQL_PORT', 'MYSQL_USER',
    #     'MYSQL_PASSWORD', 'MYSQL_DATABASE'
    # ]
    
    # missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    # if missing_vars:
    #     logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
    #     logger.error("Please set these variables in your .env file or environment")
    #     sys.exit(1)
    logger = setup_logging(enable_colors=False)
    # Create and run the job with rate limiting configuration
    logger.info("Starting stock data job with rate limiting enhancements")

    # Get rate limiting configuration from environment variables or use defaults
    max_retries = int(os.getenv('YF_MAX_RETRIES', '5'))
    min_wait = float(os.getenv('YF_MIN_WAIT', '60.0'))
    max_wait = float(os.getenv('YF_MAX_WAIT', '120.0'))
    
    from config.settings import settings
    job = StockDataJob(
        full_refresh=settings.JOB_FULL_REFRESH,
        specific_tickers=settings.JOB_SPECIFIC_TICKERS,
        batch_size=settings.JOB_BATCH_SIZE,
        delay=settings.JOB_DELAY,
        price_days=settings.JOB_PRICE_DAYS,
        max_retries=max_retries,
        min_wait=min_wait,
        max_wait=max_wait
    )
    
    success = job.run()
    
    if success:
        logger.info("Stock data job completed successfully")
        logger.info(f"Processed {job.processed_count} stocks with {job.error_count} errors")
    else:
        logger.error("Stock data job failed")
        logger.error(f"Processed {job.processed_count} stocks with {job.error_count} errors")
    
    # Exit with appropriate status code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
