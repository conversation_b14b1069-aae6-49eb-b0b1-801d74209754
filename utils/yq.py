import datetime
import sys
import os
import time


# Remove the current directory and utils directory from Python path
# This prevents yahooquery from accidentally importing utils.logging
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir in sys.path:
    sys.path.remove(current_dir)

parent_dir = os.path.dirname(current_dir)
if parent_dir in sys.path:
    sys.path.remove(parent_dir)

import yahooquery as yq
from utils.logging import setup_logging
logger = setup_logging(enable_colors=False)

def get_earnings_calendar_events(tickers, batch_size=1000):
    start_time = time.time()
    # Split tickers into batches
    ticker_batches = [tickers[i:i + batch_size] for i in range(0, len(tickers), batch_size)]
    
    logger.info(f"Processing {len(tickers)} tickers in {len(ticker_batches)} batches")
    
    all_events = []
    
    for batch_idx, batch_tickers in enumerate(ticker_batches, 1):
        batch_start_time = time.time()
        print(f"\nProcessing batch {batch_idx}/{len(ticker_batches)}: {batch_tickers}")
        
        yq_tickers = yq.Ticker(batch_tickers, formatted=True)
        events = yq_tickers.calendar_events
        for ticker, data in events.items():
            if data is None:
                continue
            earning_date = None
            eps_forecast = None
            revenue_forecast = None
            if 'earnings' in data and 'earningsDate' in data['earnings'] and len(data['earnings']['earningsDate']) >= 1 and 'isEarningsDateEstimate' in data['earnings'] and data['earnings']['isEarningsDateEstimate']:
                earning_date_str = data['earnings']['earningsDate'][0].replace(":S", "")
                earning_date = datetime.datetime.strptime(earning_date_str, '%Y-%m-%d %H:%M')
                if 'earningsAverage' in data['earnings']:
                    eps_forecast = data['earnings']['earningsAverage']
                if 'revenueAverage' in data['earnings']:
                    revenue_forecast = data['earnings']['revenueAverage']
                
                if ticker == 'FERG':
                    pass
                all_events.append({
                    'ticker': ticker,
                    'earning_date': earning_date,
                    'eps_forecast': eps_forecast,
                    'revenue_forecast': revenue_forecast
                })
        
        batch_end_time = time.time()
        batch_execution_time = batch_end_time - batch_start_time
        print(f"Batch {batch_idx} execution time: {batch_execution_time:.4f} seconds")
    
    end_time = time.time()
    execution_time = end_time - start_time
    print(f"\nTotal execution time: {execution_time:.4f} seconds")
    
    return all_events

if __name__ == "__main__":
    tickers = [
        "GME",
        # "CRWD", "FERG",
        # "AAPL", "AACB", "AACG", "AFRI", "AFYA", 
        # "9618.HK", "0001.HK", "0010.HK", "1000.HK", "1001.HK",
        # "000100.KS", "001000.KQ", "100030.KS", "100090.KS", "100130.KS",
        # "1301.T", "1332.T", "1333.T", "1352.T", "1376.T",
        # "5483.TWO", "1101.TW", "1102.TW", "1103.TW", "1104.TW"
    ]

    import yfinance as yf
    yf_ticker = yf.Ticker(tickers[0])
    calendar = yf_ticker.calendar
    print(calendar)

    get_earnings_calendar_events(tickers)
