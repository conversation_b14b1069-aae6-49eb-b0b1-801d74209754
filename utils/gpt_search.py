import os
from utils.logging import logger

def do_gpt_query(prompt, system_prompt=None, enable_web_search=False):
    params = {}
    try:
        from openai import OpenAI

        client = OpenAI(
            api_key=os.getenv('OPENAI_API_KEY'),
            base_url=os.getenv('OPENAI_API_URL')
        )
        if enable_web_search:
            params = {
                "model": "gpt-4o-search-preview",
                "web_search_options": {
                    "search_context_size": "low",
                    "user_location": {
                        "type": "approximate",
                        "approximate": {
                            "country": "US",
                            "region": "California",
                            "city": "San Francisco"
                        }
                    },
                }
            }
        else:
            params = {
                "model": "gpt-4.1-mini",
            }
        
        params["messages"] = []
        if system_prompt:
            params["messages"] = [{
                "role": "system",
                "content": system_prompt
            }]

        if len(prompt) > 10485760:
            prompt = prompt[:10485760]

        params["messages"].append({
            "role": "user",
            "content": prompt
        })

        # Create a completion with web search enabled
        completion = client.chat.completions.create(**params)
        
        # Extract search results from the response
        content = completion.choices[0].message.content
        logger.info(f"do_gpt_query with model {params["model"]} response: {content}")
        return content
    except Exception as e:
        logger.error(f"Error performing do_gpt_query: {str(e)}")
        return ''