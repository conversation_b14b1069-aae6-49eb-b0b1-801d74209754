from google.auth import default
from google.cloud import pubsub_v1
from google.api_core import exceptions
import json

from utils.logging import setup_logging

logger = setup_logging(enable_colors=False)

class PubSubPublisher:
    def __init__(self, topic_name):
        _, project_id = default()
        self.project_id = project_id
        self.topic_name = topic_name
        self.publisher = pubsub_v1.PublisherClient()
        self.topic_path = self.publisher.topic_path(self.project_id, topic_name)
        self._create_topic_if_not_exists()

        logger.info(f"PubSub publisher initialized successfully")
    
    def _create_topic_if_not_exists(self):
        """Create the topic if it doesn't exist."""
        try:
            self.publisher.get_topic(request={"topic": self.topic_path})
            logger.info(f"Topic {self.topic_path} already exists")
        except exceptions.NotFound:
            try:
                self.publisher.create_topic(request={"name": self.topic_path})
                logger.info(f"Created new topic: {self.topic_path}")
            except Exception as e:
                logger.error(f"Error creating topic {self.topic_path}: {str(e)}")
                raise

    def publish(self, data, attrs=None):
        try:
            message_data = json.dumps(data).encode('utf-8')
            
            # Convert all attribute values to strings as required by PubSub
            if attrs:
                string_attributes = {k: str(v) for k, v in attrs.items()}
            else:
                string_attributes = {}
                
            future = self.publisher.publish(self.topic_path, message_data, **string_attributes)
            message_id = future.result()
            
            logger.info(f"Published message {message_id} to {self.topic_path} with attributes: {string_attributes if attrs else 'None'}")
            return message_id
        except Exception as e:
            logger.error(f"Error publishing to Pub/Sub: {str(e)}")
            raise
