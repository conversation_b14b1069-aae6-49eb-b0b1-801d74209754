"""
Utility functions for Google Cloud Storage operations.
"""

import os
from typing import Op<PERSON>, <PERSON>ple
from pathlib import Path
from google.cloud import storage
from google.cloud.exceptions import NotFound

from utils.logging import setup_logging

# Configure logging
logger = setup_logging(enable_colors=False)

def upload_to_gcs(
    file_data: bytes, 
    object_name: str, 
    bucket_name: str, 
    content_type: str = "image/png",
    skip_existing: bool = True
) -> Optional[str]:
    """
    Upload file data to Google Cloud Storage.

    Args:
        file_data: The binary data of the file to upload
        object_name: The name to give the file in GCS (including path)
        bucket_name: The name of the GCS bucket
        content_type: The content type of the file (default: image/png)
        skip_existing: If True, skip upload if object already exists (default: True)

    Returns:
        The public URL of the uploaded file, or None if upload failed
    """
    if not file_data:
        logger.warning("No file data provided for upload")
        return None

    try:
        # Initialize GCS client
        storage_client = storage.Client()

        # Get bucket
        try:
            bucket = storage_client.get_bucket(bucket_name)
        except NotFound:
            logger.error(f"Bucket {bucket_name} not found")
            return None
            
        # Check if object already exists
        blob = bucket.blob(object_name)
        if skip_existing and blob.exists():
            logger.info(f"Object {object_name} already exists in bucket {bucket_name}, skipping upload")
            public_url = f"https://{bucket_name}/{object_name}"
            return public_url

        # Create a blob and upload the file data
        blob.upload_from_string(file_data, content_type=content_type)

        public_url = f"https://{bucket_name}/{object_name}"
        logger.info(f"File uploaded to {public_url}")

        return public_url

    except Exception as e:
        logger.error(f"Error uploading file to GCS: {str(e)}")
        return None

def get_file_extension_from_url(url: str) -> str:
    """
    Get the file extension from a URL.

    Args:
        url: The URL to extract the extension from

    Returns:
        The file extension (including the dot) or .png if no extension found
    """
    # file_extension = os.path.splitext(url)[1]
    # if not file_extension:
    #     file_extension = ".png"
    file_extension = ".png"
    return file_extension

def generate_gcs_object_name(ticker: str, save_dir: str, file_extension: str) -> str:
    """
    Generate a standardized object name for GCS.

    Args:
        ticker: The ticker symbol
        file_extension: The file extension (including the dot)

    Returns:
        A standardized object name for GCS
    """
    # Use a consistent folder structure: logos/TICKER.ext
    return f"{save_dir}/{ticker.upper()}{file_extension}"
