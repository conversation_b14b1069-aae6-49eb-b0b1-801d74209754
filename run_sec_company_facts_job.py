#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run the SEC company facts job.
"""

import os
import sys
import argparse

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from jobs.sec_company_facts_job import run_job
from utils.logging import logger

def main():
    """
    Main entry point.
    """
    parser = argparse.ArgumentParser(description='Fetch SEC XBRL company facts data for US listed stocks')
    parser.add_argument('--tickers', type=str, help='Comma-separated list of tickers to process')
    parser.add_argument('--index', type=str, choices=['sp500', 'nasdaq100', 'nasdaq', 'dow', 'russell2000'], 
                        help='Index to process')
    parser.add_argument('--workers', type=int, default=50, help='Number of concurrent workers')
    
    args = parser.parse_args()
    
    tickers = None
    if args.tickers:
        tickers = [ticker.strip() for ticker in args.tickers.split(',')]
    
    success = run_job(tickers=tickers, index=args.index, max_workers=args.workers)
    
    if not success:
        sys.exit(1)

if __name__ == '__main__':
    main()
