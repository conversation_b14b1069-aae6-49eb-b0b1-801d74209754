#!/bin/bash
set -e

# Change to the project root directory
cd "$(dirname "$0")/.."

JOB_NAME=${JOB_NAME:-"stock"}

echo "[$(date)] Starting job: ${JOB_NAME}"

# Run job based on JOB_NAME
case $JOB_NAME in
  "stock")
    python3 run_stock_data_job.py
    ;;
  "calendar")
    python3 run_financial_calendar_job.py
    ;;
  "vector")
    python3 run_vector_job.py
    ;;
  "sec_filings")
    python3 run_sec_filings_job.py
    ;;
  "real_time_filing")
    python3 run_real_time_filing_job.py
    ;;
  "sec_company_facts")
    python3 run_sec_company_facts_job.py
    ;;
  "ticker_update")
    python3 run_ticker_update_job.py
    ;;
  "earnings_calendar")
    python3 run_earnings_calendar_job.py
    ;;
  "ticker_hk_update")
    python3 run_ticker_hk_update_job.py
    ;;
  "stock_hk")
    python3 run_stock_hk_data_job.py
    ;;
  "stock_sg")
    python3 run_stock_others_data_job.py Singapore
    ;;
  "stock_jp")
    python3 run_stock_others_data_job.py Japan
    ;;
  "stock_fr")
    python3 run_stock_others_data_job.py France
    ;;
  "stock_ge")
    python3 run_stock_others_data_job.py Germany
    ;;
  "stock_kr")
    python3 run_stock_others_data_job.py Korea
    ;;
  "stock_my")
    python3 run_stock_others_data_job.py Malaysia
    ;;
  "stock_uk")
    python3 run_stock_others_data_job.py "United Kingdom"
    ;;
  "stock_th")
    python3 run_stock_others_data_job.py Thailand
    ;;
  "stock_ca")
    python3 run_stock_others_data_job.py Canada
    ;;
  "stock_tw")
    python3 run_stock_others_data_job.py Taiwan
    ;;
  "ticker_sg_update")
    python3 run_ticker_others_update_job.py Singapore
    ;;
  "ticker_jp_update")
    python3 run_ticker_others_update_job.py Japan
    ;;
  "ticker_tw_update")
    python3 run_ticker_others_update_job.py Taiwan
    ;;
  "ticker_fr_update")
    python3 run_ticker_others_update_job.py France
    ;;
  "ticker_ge_update")
    python3 run_ticker_others_update_job.py Germany
    ;;
  "ticker_kr_update")
    python3 run_ticker_others_update_job.py Korea
    ;;
  "ticker_my_update")
    python3 run_ticker_others_update_job.py Malaysia
    ;;
  "ticker_uk_update")
    python3 run_ticker_others_update_job.py United\ Kingdom
    ;;
  "ticker_th_update")
    python3 run_ticker_others_update_job.py Thailand
    ;;
  "ticker_ca_update")
    python3 run_ticker_others_update_job.py Canada
    ;;
  *)
    echo "Invalid JOB_NAME: $JOB_NAME"
    exit 1
    ;;
esac
