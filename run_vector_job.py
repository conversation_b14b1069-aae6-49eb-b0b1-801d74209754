#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run the vector job with specific tickers for testing.
"""

import os
import sys

from utils.logging import setup_logging

# Add the current directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from jobs.vector_job import run

if __name__ == '__main__':
    logger = setup_logging(enable_colors=False)
    run()
