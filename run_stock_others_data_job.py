#!/usr/bin/env python3
"""
Run the stock others data job.

This script fetches stock data for various countries using yfinance and stores them in country-specific tables.
Supports: Singapore, Japan, France, Germany, Korea, Malaysia, United Kingdom, Thailand, Canada
"""

import os
import sys
import argparse
from dotenv import load_dotenv

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from jobs.stock_others_data_job import StockOthersDataJob, COUNTRY_CONFIG
from utils.logging import setup_logging

def main():
    """
    Main entry point for the script.
    """
    parser = argparse.ArgumentParser(description="Run the stock others data job")
    parser.add_argument("country", help="Country to fetch stock data for")
    args = parser.parse_args()
    
    # Load environment variables
    load_dotenv()
    logger = setup_logging(enable_colors=False)
    
    # Validate country
    if args.country not in COUNTRY_CONFIG:
        logger.error(f"Unsupported country: {args.country}")
        logger.info(f"Supported countries: {', '.join(COUNTRY_CONFIG.keys())}")
        sys.exit(1)
    
    # Run the job
    logger.info(f"Starting stock data job for {args.country}")
    job = StockOthersDataJob(args.country)
    stats = job.run()
    
    if stats['failed'] == 0:
        logger.info(f"Stock data job for {args.country} completed successfully")
        logger.info(f"Processed {stats['success']} symbols successfully")
    else:
        logger.error(f"Stock data job for {args.country} completed with errors")
        logger.info(f"Success: {stats['success']}, Failed: {stats['failed']}")
        
    # Exit with appropriate status code
    sys.exit(0 if stats['failed'] == 0 else 0)

if __name__ == "__main__":
    main()
