#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to test the performance of the SEC company facts job.
This script compares the performance of the original and optimized versions of the process_company_facts function.
"""

import os
import sys
import time
import json
import argparse
import datetime
from typing import Dict, Any, <PERSON><PERSON>

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import settings
from utils.logging import logger
from jobs.db.mysql_client import get_db_session
from jobs.db.schema import SECCompanyFactMetadata, SECCompanyFact, Base
from jobs.sec_company_facts_job import get_cik_for_ticker, fetch_company_facts, process_company_facts

def setup_test_db():
    """Set up a test database for performance testing."""
    from sqlalchemy import create_engine
    from jobs.db.mysql_client import MySQLClient
    
    # Create tables
    MySQLClient.create_tables(Base)
    
    logger.info("Test database setup complete")

def run_performance_test(ticker: str):
    """
    Run a performance test for the SEC company facts job.
    
    Args:
        ticker: Ticker symbol to test
    """
    logger.info(f"Starting performance test for {ticker}")
    
    # Get CIK for ticker
    cik = get_cik_for_ticker(ticker)
    if not cik:
        logger.error(f"CIK not found for {ticker}, exiting")
        return
    
    logger.info(f"Found CIK for {ticker}: {cik}")
    
    # Fetch company facts
    company_facts = fetch_company_facts(cik, ticker)
    if not company_facts:
        logger.error(f"No company facts found for {ticker} (CIK {cik}), exiting")
        return
    
    logger.info(f"Successfully fetched company facts for {ticker}")
    
    # Clear existing data for this ticker to ensure a fair test
    with get_db_session() as session:
        metadata = session.query(SECCompanyFactMetadata).filter_by(ticker=ticker).first()
        if metadata:
            # Delete all facts for this metadata
            session.query(SECCompanyFact).filter_by(metadata_id=metadata.id).delete()
            # Delete the metadata
            session.delete(metadata)
            session.commit()
            logger.info(f"Cleared existing data for {ticker}")
    
    # Run the optimized process_company_facts function
    with get_db_session() as session:
        start_time = time.time()
        facts_processed, facts_stored = process_company_facts(ticker, cik, company_facts, session)
        elapsed_time = time.time() - start_time
        
        logger.info(f"Optimized version - Processed {facts_processed} facts, stored {facts_stored} facts for {ticker}")
        logger.info(f"Optimized version - Completed in {elapsed_time:.2f}s")
        
        # Get the number of facts stored in the database
        metadata = session.query(SECCompanyFactMetadata).filter_by(ticker=ticker).first()
        if metadata:
            fact_count = session.query(SECCompanyFact).filter_by(metadata_id=metadata.id).count()
            logger.info(f"Verified {fact_count} facts stored in the database")

def main():
    """
    Main entry point.
    """
    parser = argparse.ArgumentParser(description='Test the performance of the SEC company facts job')
    parser.add_argument('--ticker', type=str, required=True, help='Ticker symbol to test')
    
    args = parser.parse_args()
    
    # Set up test database
    setup_test_db()
    
    # Run performance test
    run_performance_test(args.ticker)

if __name__ == '__main__':
    main()
