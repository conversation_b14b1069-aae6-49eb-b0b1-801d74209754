import pandas as pd
from datetime import datetime, timedelta
import time
import requests

from utils.logging import setup_logging


logger = setup_logging(enable_colors=False)

# Headers (mimic browser headers, adjust as needed)
headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36",
    "Content-Type": "application/x-www-form-urlencoded",
    "X-Requested-With": "XMLHttpRequest",
    "Origin": "https://www.investing.com",
    "Referer": "https://www.investing.com/earnings-calendar/"
}

# Example payload (modify based on your filtering needs)
payload = {
    "country[]": [5],
}

def parse_date(date_str):
    """
    Parse date string in various formats to a datetime object.
    
    Args:
        date_str: String date in formats like 'Sunday, May 11, 2025' or 'May 11, 2025'
        
    Returns:
        datetime.date object or None if parsing fails
    """
    date_formats = [
        '%A, %B %d, %Y',  # Sunday, May 11, 2025
        '%B %d, %Y',      # May 11, 2025
        '%b %d, %Y',      # May 11, 2025
        '%Y-%m-%d',       # 2025-05-11
    ]
    
    for date_format in date_formats:
        try:
            return datetime.strptime(date_str, date_format).date()
        except ValueError:
            continue
    
    logger.error(f"Could not parse date: {date_str}")
    return None

def get_earnings_calendar(max_retries=3, retry_delay=5):
    """
    Alternative implementation for fetching earnings calendar data using Playwright.
    
    Args:
        max_retries: Maximum number of retry attempts
        retry_delay: Delay between retries in seconds
    
    Returns:
        DataFrame containing earnings calendar events or None if failed
    """
    url = 'https://www.investing.com/earnings-calendar/Service/getCalendarFilteredData'
    
    logger.info(f"Earnings calendar loading (alternative method)")

    for attempt in range(max_retries):
        try:
            limit_from = 0
            last_time_scope = None
            events = []
            from bs4 import BeautifulSoup
            current_date = None
            for tab in ['thisWeek', 'nextWeek']:
                while True:
                    data = get_earnings_calendar_data(url, limit_from, tab, last_time_scope)
                    bind_scroll_handler = data.get('bind_scroll_handler', True)
                    item = data.get('data', [])
                    soup = BeautifulSoup(item, 'html.parser')
                    rows = soup.find_all('tr')
                    for row in rows:
                        tds = row.find_all('td')
                        if len(tds) == 1:
                            date_str = tds[0].text.strip()
                            parsed_date = parse_date(date_str)
                            current_date = parsed_date
                            continue

                        if len(tds) != 9:
                            continue

                        company = tds[1].find('span').text.strip()
                        company_name = tds[1].get('title')
                        ticker = tds[1].find('a').text.strip()
                        eps_actual = tds[2].text.strip()
                        eps_forecast = tds[3].text.strip().replace('\xa0', '').replace('/', '')
                        revenue_actual = tds[4].text.strip()
                        revenue_forecast = tds[5].text.strip().replace('\xa0', '').replace('/', '')
                        market_cap = tds[6].text.strip()
                        release_time = tds[7].get('data-value', '')
                        if release_time == '2':
                            release_time = 0 # unknown
                        elif release_time == '1':
                            release_time = 1 # pre-market
                        elif release_time == '3':   
                            release_time = 2 # after-market
                        
                        event_details = {
                            "company": company,
                            "company_name": company_name,
                            "ticker": ticker,
                            "eps_actual": eps_actual if eps_actual != '--' else '',
                            "eps_forecast": eps_forecast if eps_forecast != '--' else '',
                            "revenue_actual": revenue_actual if revenue_actual != '--' else '',
                            "revenue_forecast": revenue_forecast if revenue_forecast != '--' else '',
                            "market_cap": market_cap,
                            "release_date": current_date,
                            "release_time": release_time,
                        }
                        
                        events.append(event_details)
                    if not bind_scroll_handler:
                        break
                    limit_from += 1
                    last_time_scope = current_date
                    time.sleep(3)
                # reset limit_from for the next tab
                limit_from = 0
                last_time_scope = None
            
            # Convert to DataFrame
            df = pd.DataFrame(events)
            return df

        except Exception as e:
            logger.error(f"Error on attempt {attempt + 1}: {str(e)}")
            
        if attempt < max_retries - 1:
            logger.info(f"Retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)
            
    logger.error("Max retries reached. Failed to fetch earnings calendar.")
    return None

def get_earnings_calendar_data(url, limit_from=0, tab='thisWeek', last_time_scope=None):
    p = {
        "country[]": payload['country[]'],
        'currentTab': tab,
        'limit_from': limit_from
    }
    if limit_from > 0:
        dt = datetime.combine(last_time_scope, datetime.min.time())
        dt_plus_8 = dt + timedelta(hours=8)
        p['last_time_scope'] = str(dt_plus_8.timestamp())
        p['submitFilters'] = 0
        p['byHandler'] = True
    response = requests.post(url, headers=headers, data=p)
    response.raise_for_status()
    data = response.json()
    return data