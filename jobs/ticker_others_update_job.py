import os
import sys
import time
import traceback
from typing import Dict, List, Optional
from sqlalchemy import text

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from jobs.db.mysql_client import get_db_session
from jobs.db.schema import JobRun, AllTickerSG, AllTickerJP, AllTickerFR, AllTickerGE, AllTickerCA, AllTickerKR, AllTickerMY, AllTickerTH, AllTickerUK, AllTickerTW
from jobs.db.migrations.migrations import apply_migrations
from utils.logging import setup_logging
from utils.image_utils import download_logo
from utils.ticker_utils import strip_leading_zeros, generate_yf_symbol
from config.settings import settings

# Configure logging
logger = setup_logging(enable_colors=False)

# Get GCS bucket name from settings
GCS_BUCKET = os.getenv('GCS_LOGO_BUCKET', 'images.dev.addxgo.io/tickers')

class TickerOthersUpdateJob:
    def __init__(self, country: str):
        self.country = country
        self.processed_count = 0
        self.error_count = 0
        self.batch_counter = 0
        self.job_run_id = None

        # Country configuration mapping
        self.country_config = {
            'Singapore': {
                'model': AllTickerSG,
                'investpy_country': 'singapore',
                'table_name': 'all_tickers_sg'
            },
            'Japan': {
                'model': AllTickerJP,
                'investpy_country': 'japan',
                'table_name': 'all_tickers_jp'
            },
            'France': {
                'model': AllTickerFR,
                'investpy_country': 'france',
                'table_name': 'all_tickers_fr'
            },
            'Germany': {
                'model': AllTickerGE,
                'investpy_country': 'germany',
                'table_name': 'all_tickers_ge'
            },
            'Korea': {
                'model': AllTickerKR,
                'investpy_country': 'south korea',
                'table_name': 'all_tickers_kr'
            },
            'Malaysia': {
                'model': AllTickerMY,
                'investpy_country': 'malaysia',
                'table_name': 'all_tickers_my'
            },
            'United Kingdom': {
                'model': AllTickerUK,
                'investpy_country': 'united kingdom',
                'table_name': 'all_tickers_uk'
            },
            'Canada': {
                'model': AllTickerCA,
                'investpy_country': 'canada',
                'table_name': 'all_tickers_ca'
            },
            'Thailand': {
                'model': AllTickerTH,
                'investpy_country': 'thailand',
                'table_name': 'all_tickers_th'
            },
            'Taiwan': {
                'model': AllTickerTW,
                'investpy_country': 'taiwan',
                'table_name': 'all_tickers_tw'
            }
        }

        if country not in self.country_config:
            raise ValueError(f"Unsupported country: {country}. Supported countries: {list(self.country_config.keys())}")

        self.config = self.country_config[country]

        # Initialize database
        self._init_database()

    def _init_database(self):
        """
        Initialize the database.
        """
        logger.info("Initializing database")
        try:
            # Apply migrations instead of creating tables directly
            apply_migrations()
            logger.info("Database initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise

    def run(self):
        """
        Run the job.
        """
        logger.info(f"Starting {self.country} ticker update job")
        start_time = time.time()

        try:
            # Create job run record and store its ID
            with get_db_session() as session:
                job_run = JobRun.create_job_run(session, f"ticker_{self.country.lower()}_update_job")
                session.flush()  # Ensure we have the ID
                self.job_run_id = job_run.id
                session.commit()
                logger.info(f"Created job run record with ID: {self.job_run_id}")

            self._get_tickers_from_investpy()

            # Mark job as completed using a fresh session
            with get_db_session() as session:
                job_run = session.query(JobRun).get(self.job_run_id)
                job_run.complete(session, self.processed_count)

            end_time = time.time()
            logger.info(f"Job completed successfully in {end_time - start_time:.2f} seconds")
            return True
        except Exception as e:
            logger.error(f"Error running job: {str(e)}")
            logger.error(traceback.format_exc())

            # Mark job as failed using a fresh session
            if self.job_run_id:
                with get_db_session() as session:
                    job_run = session.query(JobRun).get(self.job_run_id)
                    if job_run:
                        job_run.fail(session, str(e), self.processed_count)

            return False

    def _get_tickers_from_investpy(self):
        """
        Get stock tickers from investpy API for the specified country.
        """
        try:
            import investpy

            logger.info(f"Getting stocks for {self.country} using investpy")

            # Get all stocks for the specified country
            stocks_df = investpy.get_stocks(country=self.config['investpy_country'])
            logger.info(f"Retrieved {len(stocks_df)} stocks from investpy for {self.country}")

            # Prepare data for database
            ticker_info_list = []
            seen_symbols = set()  # Track symbols to avoid duplicates
            duplicates_removed = 0

            for _, row in stocks_df.iterrows():
                # Process symbol: strip leading zeros
                original_symbol = row['symbol'] if 'symbol' in row else None
                if original_symbol:
                    stripped_symbol = strip_leading_zeros(original_symbol)
                    # Map country to region code for yf_symbol generation
                    region_map = {
                        'Japan': 'JP',
                        'Taiwan': 'TW',
                        'Korea': 'KR',
                        'Singapore': 'SG',
                        'France': 'FR',
                        'Germany': 'GE',
                        'Canada': 'CA',
                        'Malaysia': 'MY',
                        'Thailand': 'TH',
                        'United Kingdom': 'UK'
                    }
                    region_code = region_map.get(self.country)
                    yf_symbol = generate_yf_symbol(stripped_symbol, region_code)
                else:
                    stripped_symbol = None
                    yf_symbol = None

                ticker_info = {
                    'symbol': stripped_symbol,  # Store symbol without leading zeros
                    'yf_symbol': yf_symbol,     # Store Yahoo Finance compatible symbol
                    'name': row['name'] if 'name' in row else None,
                    'country': self.country,
                    'currency': row['currency'] if 'currency' in row else None,
                    'exchange': row['exchange'] if 'exchange' in row else None,
                    'isin': row['isin'] if 'isin' in row else None,
                }

                # Only add if we have essential data and symbol is not duplicate
                if ticker_info['symbol'] and ticker_info['name']:
                    if ticker_info['symbol'] not in seen_symbols:
                        ticker_info_list.append(ticker_info)
                        seen_symbols.add(ticker_info['symbol'])
                    else:
                        duplicates_removed += 1
                        logger.debug(f"Removed duplicate symbol: {ticker_info['symbol']}")

            logger.info(f"Processed {len(ticker_info_list)} valid tickers for {self.country}")
            if duplicates_removed > 0:
                logger.info(f"Removed {duplicates_removed} duplicate symbols from investpy data")

            # Save tickers to database
            self._save_tickers_to_db(ticker_info_list)

            return ticker_info_list

        except ImportError:
            logger.error("investpy package is not installed. Please install it with 'pip install investpy'")
            raise
        except Exception as e:
            logger.error(f"Error getting {self.country} tickers from investpy: {str(e)}")
            raise

    def _save_tickers_to_db(self, ticker_data: List[Dict]):
        """
        Save tickers to the database using UPSERT (INSERT ... ON DUPLICATE KEY UPDATE).
        This prevents duplicate key errors by updating existing records instead of failing.

        Args:
            ticker_data: List of dictionaries containing ticker information
        """
        logger.info(f"Saving {len(ticker_data)} {self.country} tickers to database using UPSERT")
        batch_size = 20
        batches = [ticker_data[i:i + batch_size] for i in range(0, len(ticker_data), batch_size)]

        total_processed = 0

        with get_db_session() as session:
            for batch in batches:
                # Prepare batch data for UPSERT
                batch_values = []
                for ticker_info in batch:
                    logo_path = None
                    if not settings.SKIP_UPDATE_LOGO and ticker_info['symbol']:
                        from utils.logo_api import get_logo_url
                        from utils.ticker_utils import convert_jp_ticker_to_yf, convert_tw_ticker_to_yf, convert_kr_ticker_to_yf
                        # convert symbol
                        symbol = ticker_info['symbol']
                        save_dir = ''
                        if self.country == 'Japan':
                            symbol = convert_jp_ticker_to_yf(symbol)
                            save_dir = '/jp'
                        elif self.country == 'Taiwan':
                            symbol = convert_tw_ticker_to_yf(symbol)
                            save_dir = '/tw'
                        elif self.country == 'Korea':
                            symbol = convert_kr_ticker_to_yf(symbol)
                            save_dir = '/kr'
                        logo_url = get_logo_url(symbol, ticker_info['name'])
                        if logo_url:
                            file_path, _ = download_logo(symbol, logo_url, save_dir)
                            if file_path:
                                logo_path = file_path

                    batch_values.append({
                        'symbol': ticker_info['symbol'],
                        'yf_symbol': ticker_info['yf_symbol'],
                        'name': ticker_info['name'],
                        'country': ticker_info['country'],
                        'currency': ticker_info['currency'],
                        'exchange': ticker_info['exchange'],
                        'isin': ticker_info['isin'],
                        'logo': logo_path
                    })

                # Execute UPSERT using raw SQL for better performance and reliability
                table_name = self.config['table_name']
                upsert_sql = f"""
                INSERT INTO {table_name}
                (symbol, yf_symbol, name, country, currency, exchange, isin, logo, created_at, updated_at)
                VALUES
                (:symbol, :yf_symbol, :name, :country, :currency, :exchange, :isin, :logo, NOW(), NOW())
                ON DUPLICATE KEY UPDATE
                    yf_symbol = VALUES(yf_symbol),
                    name = VALUES(name),
                    country = VALUES(country),
                    currency = VALUES(currency),
                    exchange = VALUES(exchange),
                    isin = VALUES(isin),
                    logo = COALESCE(VALUES(logo), logo),
                    updated_at = NOW()
                """

                try:
                    # Execute the UPSERT for the entire batch
                    result = session.execute(text(upsert_sql), batch_values)
                    session.commit()

                    total_processed += len(batch)
                    self.batch_counter += 1
                    logger.info(f"Successfully processed batch {self.batch_counter} with {len(batch)} tickers")

                except Exception as e:
                    session.rollback()
                    logger.error(f"Error processing batch {self.batch_counter + 1}: {str(e)}")
                    # Try individual inserts for this batch to identify problematic records
                    self._handle_batch_error(session, batch_values, table_name)

            # Update processed count
            self.processed_count = total_processed

        logger.info(f"Successfully processed {total_processed} tickers for {self.country} using UPSERT")

    def _handle_batch_error(self, session, batch_values: List[Dict], table_name: str):
        """
        Handle batch errors by processing records individually to identify issues.

        Args:
            session: Database session
            batch_values: List of ticker data dictionaries
            table_name: Name of the target table
        """
        logger.info(f"Processing {len(batch_values)} records individually due to batch error")

        individual_upsert_sql = f"""
        INSERT INTO {table_name}
        (symbol, yf_symbol, name, country, currency, exchange, isin, logo, created_at, updated_at)
        VALUES
        (:symbol, :yf_symbol, :name, :country, :currency, :exchange, :isin, :logo, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
            yf_symbol = VALUES(yf_symbol),
            name = VALUES(name),
            country = VALUES(country),
            currency = VALUES(currency),
            exchange = VALUES(exchange),
            isin = VALUES(isin),
            logo = COALESCE(VALUES(logo), logo),
            updated_at = NOW()
        """

        success_count = 0
        error_count = 0

        for ticker_data in batch_values:
            try:
                session.execute(text(individual_upsert_sql), ticker_data)
                session.commit()
                success_count += 1
            except Exception as e:
                session.rollback()
                error_count += 1
                logger.error(f"Failed to process ticker {ticker_data.get('symbol', 'UNKNOWN')}: {str(e)}")

        logger.info(f"Individual processing completed: {success_count} successful, {error_count} failed")


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python ticker_others_update_job.py <country>")
        print("Supported countries: Singapore, Japan, France, Germany, Korea, Malaysia, United Kingdom, Canada, Thailand, Taiwan")
        sys.exit(1)

    country = sys.argv[1]
    job = TickerOthersUpdateJob(country)
    success = job.run()
    sys.exit(0 if success else 1)
