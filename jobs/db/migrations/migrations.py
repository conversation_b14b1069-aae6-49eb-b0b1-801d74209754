from yoyo import get_backend, read_migrations
from config.settings import settings
import os

from utils.logging import setup_logging


logger = setup_logging(enable_colors=False)

def apply_migrations():
    # Convert SQLAlchemy URI to yoyo format
    yoyo_uri = settings.MYSQL_URI.replace('mysql+pymysql://', 'mysql://')
    
    # Get absolute path to migrations directory
    migrations_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "sqls"))
    logger.info(f"Looking for migrations in: {migrations_dir}")
    
    # Define MySQL connection
    backend = get_backend(yoyo_uri)
    
    # Load migrations
    migrations = read_migrations(migrations_dir)

    # Apply pending migrations
    with backend.lock():
        backend.apply_migrations(backend.to_apply(migrations))

