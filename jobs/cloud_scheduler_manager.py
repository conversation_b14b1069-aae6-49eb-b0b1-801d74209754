import os
import re
from typing import List
from google.cloud import run_v2
from google.cloud import scheduler_v1
from google.protobuf.json_format import MessageToDict
from google.auth import default
import requests
from config.settings import settings
from jobs.db.schema import FinancialCalendarEvent
from utils.logging import setup_logging

logger = setup_logging(enable_colors=False)

# cloud run's job docs: https://cloud.google.com/python/docs/reference/run/latest/google.cloud.run_v2.services.jobs.JobsClient#google_cloud_run_v2_services_jobs_JobsClient_create_job
# scheduler docs: https://cloud.google.com/python/docs/reference/cloudscheduler/latest/google.cloud.scheduler_v1.services.cloud_scheduler.CloudSchedulerClient#google_cloud_scheduler_v1_services.cloud_scheduler_CloudSchedulerClient_create_job
class CloudSchedulerManager:
    def __init__(self):
        """Initialize Cloud Scheduler client using application default credentials."""
        self.job_client = run_v2.JobsClient()
        self.scheduler_client = scheduler_v1.CloudSchedulerClient()
        # Get project from credentials
        _, project_id = default()
        self.project_id = project_id
        # Get location from metadata server
        try:
            response = requests.get(
                'http://metadata.google.internal/computeMetadata/v1/instance/zone',
                headers={'Metadata-Flavor': 'Google'},
                timeout=2
            )
            # Convert zone (e.g. us-central1-a) to location (us-central1)
            self.location = '-'.join(response.text.split('/')[-1].split('-')[:-1])
            # for debug
            self.location = 'asia-southeast1'  # Default location if metadata not available
        except Exception as e:
            logger.warning(f"Could not get location from metadata, using default: {str(e)}")
            self.location = 'asia-southeast1'  # Default location if metadata not available
            
        logger.info(f"Using project: {project_id}, location: {self.location}")
        self.parent = f"projects/{project_id}/locations/{self.location}"
        self.job_tpl_name = f"projects/{project_id}/locations/{self.location}/jobs/{settings.ROOT_JOB_NAME}"

    def _clean_job_id(self, text: str) -> str:
        # Replace any non-alphanumeric character with hyphen
        cleaned = re.sub(r'[^a-zA-Z0-9]', '-', text)
        # Replace multiple consecutive hyphens with a single hyphen
        cleaned = re.sub(r'-+', '-', cleaned)
        # Remove trailing hyphen if exists
        return cleaned.rstrip("-")

    def _get_job_id(self, event: FinancialCalendarEvent) -> str:
        date_without_year = event.date_time.strftime('%m-%d-%H-%M')
        run_job_id = f'event-{date_without_year}-{self._clean_job_id(event.event_name)}'.lower()
        if len(run_job_id) > 63:
            run_job_id = run_job_id[:63]
        return run_job_id

    def create_run_job_if_not_exist(self, event: FinancialCalendarEvent):
        event_id = event.id
        run_job_id = self._get_job_id(event)
        run_job_name = f"projects/{self.project_id}/locations/{self.location}/jobs/{run_job_id}"
        try:
            self.job_client.get_job(name=run_job_name)
            # job exists
            logger.info(f"Run job {run_job_id} already exists, skip")
            return
        except:
            # job not exists will exception
            pass
        tpl_job = self.job_client.get_job(name=self.job_tpl_name)
        tpl_job_config = MessageToDict(tpl_job._pb)

        from config.settings import settings

        run_job = run_v2.Job()
        run_job.labels = {
            'event_id': str(event_id),
            'event_name': event.event_name,
            'event_date_time': str(event.date_time),
        }
        run_job.template = run_v2.ExecutionTemplate(
            template=run_v2.TaskTemplate(
                containers=[
                    run_v2.Container(
                        image=tpl_job_config.get('template').get('template').get('containers')[0].get('image'),
                        env=[{
                            "name": "JOB_NAME",
                            "value": "calendar",
                        }, {
                            "name": "FINANCIAL_CALENDAR_EVENT_ID",
                            "value": str(event_id),
                        }, {
                            "name": "MYSQL_HOST",
                            "value": settings.MYSQL_HOST,
                        },{
                            "name": "MYSQL_USER",
                            "value": settings.MYSQL_USER,
                        },{
                            "name": "MYSQL_PASSWORD",
                            "value": os.getenv("MYSQL_PASSWORD"),
                        },{
                            "name": "MYSQL_PORT",
                            "value": str(settings.MYSQL_PORT),
                        },{
                            "name": "FINANCIAL_CALENDAR_SOURCE",
                            "value": "investing",
                        },{
                            "name": "ENABLE_PUBSUB",
                            "value": "true",
                        },{
                            "name": "PUBSUB_FINANCIAL_CALENDAR_TOPIC",
                            "value": settings.PUBSUB_FINANCIAL_CALENDAR_TOPIC,
                        },{
                            "name": "CREATE_TRIGGER_SERVICE_ACCOUNT",
                            "value": settings.CREATE_TRIGGER_SERVICE_ACCOUNT,
                        },{
                            "name": "OPENAI_API_KEY",
                            "value": settings.OPENAI_API_KEY,
                        },{
                            "name": "FINANCIAL_CALENDAR_NOTIFICATION_FOR_DEBUG",
                            "value": 'true' if settings.FINANCIAL_CALENDAR_NOTIFICATION_FOR_DEBUG else 'false',
                        }],
                        resources=run_v2.ResourceRequirements(
                            cpu_idle=False,
                            limits={
                                "cpu": "2",
                                "memory": "2Gi",
                            },
                        )
                    )
                ],
                volumes=[
                    run_v2.Volume(
                        name=tpl_job_config.get('template').get('template').get('volumes')[0].get('name'),
                        cloud_sql_instance=run_v2.CloudSqlInstance(
                            instances=tpl_job_config.get('template').get('template').get('volumes')[0].get('cloudSqlInstance').get('instances'),
                        ),
                    )
                ],
                vpc_access=run_v2.VpcAccess(
                    egress=run_v2.VpcAccess.VpcEgress.PRIVATE_RANGES_ONLY,
                    network_interfaces=[
                        run_v2.VpcAccess.NetworkInterface(
                            network=tpl_job_config.get('template').get('template').get('vpcAccess').get('networkInterfaces')[0].get('network'),
                            subnetwork=tpl_job_config.get('template').get('template').get('vpcAccess').get('networkInterfaces')[0].get('subnetwork'),
                        ),
                    ],
                ),
                max_retries=3,
            ),
            task_count=1,
        )
        job_request = run_v2.CreateJobRequest(
            parent=self.parent,
            job_id=run_job_id,
            job=run_job,
        )
        try:
            _ = self.job_client.create_job(request=job_request)
            logger.info(f"Run job {run_job_id} create success")
            return
        except Exception as e:
            logger.warning(f"Run job {run_job_id} create failed: {str(e)}")
            pass

    def create_scheduler_trigger_if_not_exist(self, event: FinancialCalendarEvent, delay=1):
        event_id = event.id
        event_time = event.date_time
        expr = date_time_to_cron(event_time, delay)
        if expr == '':
            logger.warning(f"Invalid event time {event_time}, skip")
            return
        run_job_id = self._get_job_id(event)
        scheduler_trigger_id = f'event-{str(event_id)}-trigger'
        scheduler_trigger_name =  f"projects/{self.project_id}/locations/{self.location}/jobs/{scheduler_trigger_id}"

        try:
            self.scheduler_client.get_job(name=scheduler_trigger_name)
            # trigger exists
            logger.info(f"Trigger {scheduler_trigger_id} already exists, skip")
            return
        except:
            # trigger not exists will exception
            pass

        run_job_uri = f"https://{self.location}-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/{self.project_id}/jobs/{run_job_id}:run"
        scheduler_trigger = scheduler_v1.Job(
            name=scheduler_trigger_name,
            description=f"trigger for {run_job_id}",
            http_target=scheduler_v1.HttpTarget(
                uri=run_job_uri,
                http_method=scheduler_v1.HttpMethod.POST,
                headers={
                    "User-Agent": "Google-Cloud-Scheduler"
                },
                oauth_token=scheduler_v1.OAuthToken(
                    service_account_email=settings.CREATE_TRIGGER_SERVICE_ACCOUNT,
                    scope="https://www.googleapis.com/auth/cloud-platform",
                )
            ),
            schedule=expr,
            # investing is GMT - 4
            time_zone='America/Manaus',
        )
        scheduler_trigger_request = scheduler_v1.CreateJobRequest(
            parent=self.parent,
            job=scheduler_trigger,
        )

        try:
            _ = self.scheduler_client.create_job(request=scheduler_trigger_request)
            logger.info(f"Scheduler trigger {scheduler_trigger_id}({expr}) create success")
            return
        except Exception as e:
            logger.warning(f"Scheduler trigger {scheduler_trigger_id}({expr}) create failed: {str(e)}")
            pass

    def delete_run_job_if_exists(self, event: FinancialCalendarEvent):
        run_job_id = self._get_job_id(event)
        run_job_name = f"projects/{self.project_id}/locations/{self.location}/jobs/{run_job_id}"
        try:
            self.job_client.get_job(name=run_job_name)
        except Exception as e:
            if '404 Resource' in str(e):
                logger.info(f"Event {event.id} run job {run_job_id} not found, nothing to delete")
                return True
            logger.warning(f"Cannot find event {event.id} run job {run_job_id}: {str(e)}")
            return False  # Return True if job doesn't exist (nothing to delete)
        
        try:
            self.job_client.delete_job(name=run_job_name)
            logger.info(f"Run job {run_job_id} delete success")
            return True
        except Exception as e:
            logger.warning(f"Run job {run_job_id} delete failed: {str(e)}")
            return False

    def delete_scheduler_trigger_if_exists(self, event: FinancialCalendarEvent):
        event_id = event.id
        scheduler_trigger_id = f'event-{str(event_id)}-trigger'
        scheduler_trigger_name =  f"projects/{self.project_id}/locations/{self.location}/jobs/{scheduler_trigger_id}"
        try:
            self.scheduler_client.get_job(name=scheduler_trigger_name)
        except Exception as e:
            if 'Job not found' in str(e):
                logger.info(f"Event {event_id} scheduler trigger {scheduler_trigger_id} not found, nothing to delete")
                return True
            logger.warning(f"Cannot find event {event_id} scheduler trigger {scheduler_trigger_id}: {str(e)}")
            return False  # Return True if trigger doesn't exist (nothing to delete)
        
        try:
            self.scheduler_client.delete_job(name=scheduler_trigger_name)
            logger.info(f"Scheduler trigger {scheduler_trigger_id} delete success")
            return True
        except Exception as e:
            logger.warning(f"Scheduler trigger {scheduler_trigger_id} delete failed: {str(e)}")
            return False

    def schedule_events(self, events: List[FinancialCalendarEvent]):
        """Schedule events by copying template job and updating parameters."""
        try:
            for event in events:
                self.create_run_job_if_not_exist(event)
                # create delay one minute trigger by event.time
                self.create_scheduler_trigger_if_not_exist(event, 1)

        except Exception as e:
            logger.error(f"Error in scheduling events: {str(e)}")
            raise
    
    def remove_monitor_event(self, event: FinancialCalendarEvent):
        """Remove cloud run job and scheduler trigger for the event.
        Note: Caller needs to commit the database changes after this call.
        """
        try:
            run_job_deleted = self.delete_run_job_if_exists(event)
            scheduler_trigger_deleted = self.delete_scheduler_trigger_if_exists(event)
            
            if run_job_deleted and scheduler_trigger_deleted:
                event.is_completed = True
                logger.info(f"Event {event.id} marked as completed")
            else:
                logger.warning(f"Event {event.id} not marked as completed due to deletion failures")
                
            return event  # Return modified event for caller to commit
        except Exception as e:
            logger.error(f"Failed to remove monitor event {event.id}: {str(e)}")
            raise

def date_time_to_cron(date_time, delay=0):
    hours = date_time.hour
    minutes = date_time.minute
    day = date_time.day
    month = date_time.month
    
    if not (0 <= hours <= 23 and 0 <= minutes <= 59):
        return ''
    
    if delay > 0:
        from datetime import timedelta
        date_time = date_time + timedelta(minutes=delay)
        hours = date_time.hour
        minutes = date_time.minute
        day = date_time.day
        month = date_time.month
    
    # Added year to cron expression at the end
    cron_expr = f"{minutes} {hours} {day} {month} *"
    return cron_expr