import os
import sys
import time
import traceback
from typing import Dict, List, Optional

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from jobs.db.mysql_client import get_db_session
from jobs.db.schema import JobRun, AllTickerHK
from jobs.db.migrations.migrations import apply_migrations
from utils.logging import setup_logging
from utils.image_utils import download_logo
from utils.ticker_utils import convert_hk_ticker_to_yf, strip_leading_zeros, generate_yf_symbol
from config.settings import settings

# Configure logging
logger = setup_logging(enable_colors=False)

# Get GCS bucket name from settings
GCS_BUCKET = os.getenv('GCS_LOGO_BUCKET', 'images.dev.addxgo.io/tickers')

class TickerHKUpdateJob:
    def __init__(self):
        self.processed_count = 0
        self.error_count = 0
        self.batch_counter = 0
        self.job_run_id = None
        # Initialize database
        self._init_database()

    def _init_database(self):
        """
        Initialize the database.
        """
        logger.info("Initializing database")
        try:
            # Apply migrations instead of creating tables directly
            apply_migrations()
            logger.info("Database initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise

    def run(self):
        """
        Run the job.
        """
        logger.info("Starting Hong Kong ticker update job")
        start_time = time.time()

        try:
            # Create job run record and store its ID
            with get_db_session() as session:
                job_run = JobRun.create_job_run(session, "ticker_hk_update_job")
                session.flush()  # Ensure we have the ID
                self.job_run_id = job_run.id
                session.commit()
                logger.info(f"Created job run record with ID: {self.job_run_id}")

            self._get_hk_tickers_from_tushare()

            # Mark job as completed using a fresh session
            with get_db_session() as session:
                job_run = session.query(JobRun).get(self.job_run_id)
                job_run.complete(session, self.processed_count)

            end_time = time.time()
            logger.info(f"Job completed successfully in {end_time - start_time:.2f} seconds")
            return True
        except Exception as e:
            logger.error(f"Error running job: {str(e)}")
            logger.error(traceback.format_exc())

            # Mark job as failed using a fresh session
            if self.job_run_id:
                with get_db_session() as session:
                    job_run = session.query(JobRun).get(self.job_run_id)
                    if job_run:
                        job_run.fail(session, str(e), self.processed_count)

            return False

    def _get_hk_tickers_from_tushare(self):
        """
        Get Hong Kong stock tickers from Tushare API.
        """
        try:
            import tushare as ts

            # Check if TUSHARE_TOKEN is set in environment variables
            tushare_token = os.getenv('TUSHARE_TOKEN')
            if not tushare_token:
                logger.error("TUSHARE_TOKEN environment variable is not set")
                raise ValueError("TUSHARE_TOKEN environment variable is not set")

            # Initialize Tushare with token
            pro = ts.pro_api(tushare_token)
            logger.info("Successfully initialized Tushare API")

            # Get all Hong Kong stocks (default is list_status='L' for listed stocks)
            hk_stocks = pro.hk_basic()
            logger.info(f"Retrieved {len(hk_stocks)} Hong Kong stocks from Tushare")

            # Prepare data for database
            ticker_info_list = []
            for _, row in hk_stocks.iterrows():
                # Process symbol: strip leading zeros from ts_code
                original_symbol = row['ts_code']
                stripped_symbol = strip_leading_zeros(original_symbol)
                yf_symbol = generate_yf_symbol(stripped_symbol, 'HK')

                ticker_info = {
                    'symbol': stripped_symbol,  # Store symbol without leading zeros
                    'yf_symbol': yf_symbol,     # Store Yahoo Finance compatible symbol
                    'name': row['enname'] if 'enname' in row and row['enname'] else row['name'],  # Use English name as primary, fallback to Chinese
                    'fullname': row['fullname'] if 'fullname' in row else None,
                    'cnname': row['name'],  # Chinese name goes to cnname field
                    'cn_spell': row['cn_spell'] if 'cn_spell' in row else None,
                    'market': row['market'] if 'market' in row else None,
                    'list_status': row['list_status'] if 'list_status' in row else None,
                    'list_date': row['list_date'] if 'list_date' in row else None,
                    'delist_date': row['delist_date'] if 'delist_date' in row else None,
                    'trade_unit': row['trade_unit'] if 'trade_unit' in row else None,
                    'isin': row['isin'] if 'isin' in row else None,
                    'curr_type': row['curr_type'] if 'curr_type' in row else None,
                }
                ticker_info_list.append(ticker_info)

            # Save tickers to database
            self._save_tickers_to_db(ticker_info_list)

            return ticker_info_list

        except ImportError:
            logger.error("Tushare package is not installed. Please install it with 'pip install tushare'")
            raise
        except Exception as e:
            logger.error(f"Error getting Hong Kong tickers from Tushare: {str(e)}")
            raise

    def _save_tickers_to_db(self, ticker_data: List[Dict]):
        """
        Save tickers to the database.

        Args:
            ticker_data: List of dictionaries containing ticker information
        """
        logger.info(f"Saving {len(ticker_data)} Hong Kong tickers to database")
        batch_size = 20
        batches = [ticker_data[i:i + batch_size] for i in range(0, len(ticker_data), batch_size)]

        total_saved = 0
        total_updated = 0
        total_logos_processed = 0

        with get_db_session() as session:
            # Get existing tickers for comparison
            existing_tickers = {ticker.symbol: ticker for ticker in session.query(AllTickerHK).all()}

            for batch in batches:
                for ticker_info in batch:
                    symbol = ticker_info['symbol']

                    # Process logo if needed
                    logo_path = None
                    if not settings.SKIP_UPDATE_LOGO:
                        # Use the yf_symbol for logo fetching
                        yf_symbol = ticker_info.get('yf_symbol', symbol)
                        if yf_symbol:
                            from utils.logo_api import get_logo_url
                            logo_url = get_logo_url(yf_symbol, ticker_info['name'])

                            if logo_url:
                                # Use download_logo function from image_utils.py to download and upload the logo to GCS
                                file_path, _ = download_logo(yf_symbol, logo_url, '/hk')

                                if file_path:
                                    # Store the GCS URL in the database
                                    logo_path = file_path
                                    total_logos_processed += 1
                                    logger.info(f"Downloaded and uploaded logo for ticker {symbol} to GCS: {file_path}")
                                else:
                                    logger.warning(f"Failed to download logo for ticker {symbol}")

                    if symbol in existing_tickers:
                        # Update existing ticker
                        existing = existing_tickers[symbol]

                        # Update fields
                        existing.name = ticker_info['name']
                        existing.fullname = ticker_info['fullname']
                        existing.cnname = ticker_info['cnname']  # Update cnname field (was enname)
                        existing.cn_spell = ticker_info['cn_spell']
                        existing.market = ticker_info['market']
                        existing.list_status = ticker_info['list_status']
                        existing.list_date = ticker_info['list_date']
                        existing.delist_date = ticker_info['delist_date']
                        existing.trade_unit = ticker_info['trade_unit']
                        existing.isin = ticker_info['isin']
                        existing.curr_type = ticker_info['curr_type']
                        existing.yf_symbol = ticker_info['yf_symbol']  # Update yf_symbol field

                        # Update logo field if available
                        if logo_path:
                            existing.logo = logo_path

                        total_updated += 1
                    else:
                        # Create new AllTickerHK object and add to session
                        new_ticker = AllTickerHK(
                            symbol=symbol,
                            name=ticker_info['name'],
                            fullname=ticker_info['fullname'],
                            cnname=ticker_info['cnname'],  # Use cnname field (was enname)
                            cn_spell=ticker_info['cn_spell'],
                            market=ticker_info['market'],
                            list_status=ticker_info['list_status'],
                            list_date=ticker_info['list_date'],
                            delist_date=ticker_info['delist_date'],
                            trade_unit=ticker_info['trade_unit'],
                            isin=ticker_info['isin'],
                            curr_type=ticker_info['curr_type'],
                            yf_symbol=ticker_info['yf_symbol'],  # Add yf_symbol field
                            logo=logo_path
                        )
                        session.add(new_ticker)
                        total_saved += 1

                # Commit each batch
                session.commit()
                self.batch_counter += 1
                logger.info(f"Committed batch {self.batch_counter} with {len(batch)} tickers")

            # Update processed count
            self.processed_count = total_saved + total_updated

        logger.info(f"Saved {total_saved} new tickers and updated {total_updated} existing tickers")
        logger.info(f"Processed {total_logos_processed} logos")


if __name__ == "__main__":
    job = TickerHKUpdateJob()
    job.run()
