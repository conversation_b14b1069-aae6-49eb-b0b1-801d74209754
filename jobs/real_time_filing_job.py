"""
Real-time filing job implementation for fetching SEC filings for companies with earnings today.
"""

from datetime import datetime, timedelta
import pytz
import traceback
import requests
import re
import html2text
import json

from sqlalchemy import and_
from jobs.db.migrations.migrations import apply_migrations
from jobs.db.mysql_client import get_db_session
from jobs.db.schema import (
    EarningsCalendarEvent,
    SECFiling,
    JobRun,
    Base
)
from utils.gpt_search import do_gpt_query
from utils.logging import setup_logging
from utils.sec_filings import get_filings_by_cik
from utils.ticker_cik import get_ticker_to_cik_map, get_ticker_to_cik_map_from_stock_mapper
from utils.yh import get_filing_fiscal_year_and_quarter

logger = setup_logging(enable_colors=False)

def get_market_time() -> str:
    """
    Determine if it's currently pre-market, market hours, or after-market time.

    Returns:
        str: 'pre-market', 'market-hours', or 'after-market'
    """
    # Define Eastern Time Zone
    eastern = pytz.timezone('US/Eastern')

    # Get current time in Eastern Time
    now = datetime.now(eastern)

    # Extract hour and minute
    hour = now.hour
    minute = now.minute
    current_time = hour * 60 + minute  # Convert to minutes for easier comparison

    # Define market hours in minutes
    pre_market_start = 4 * 60  # 4:00 AM ET
    market_open = 9 * 60 + 30  # 9:30 AM ET
    market_close = 16 * 60     # 4:00 PM ET
    after_market_end = 20 * 60 # 8:00 PM ET

    # Check if it's a weekday (0 = Monday, 4 = Friday)
    is_weekday = 0 <= now.weekday() <= 4

    if not is_weekday:
        return "weekend"
    elif current_time < pre_market_start:
        return "overnight"
    elif pre_market_start <= current_time < market_open:
        return "pre-market"
    elif market_open <= current_time < market_close:
        return "market-hours"
    elif market_close <= current_time < after_market_end:
        return "after-market"
    else:
        return "overnight"

class RealTimeFilingJob:
    """
    Job for fetching SEC filings for companies with earnings today.
    """

    def __init__(self):
        apply_migrations()
        self.create_count = 0
        self.gpt_search_count = 0
        self.skip_count = 0

    def process_todays_earnings_events(self):
        """
        Get earnings calendar events for today.

        Returns:
            List of earnings calendar events for today
        """
        today = datetime.now().date() - timedelta(days=3)
        market_time = get_market_time()
        # if market_time != 'pre-market' or market_time != 'after-market':
        #     logger.info(f"Skip processing today's earnings events, current time is {market_time}")
        #     return

        with get_db_session() as session:
            try:
                # First try to get events from the database
                query = session.query(EarningsCalendarEvent).where(
                    and_(
                        EarningsCalendarEvent.release_date < today + timedelta(days=1),
                        EarningsCalendarEvent.is_filing_processed == 0,
                    )
                )

                # Print the actual SQL query with parameter bindings
                compiled_query = query.statement.compile(
                    compile_kwargs={"literal_binds": True},
                    dialect=session.bind.dialect
                )
                logger.info(f"Generated SQL: {compiled_query}")

                events = query.all()

                need_process_events = []
                for event in events:
                    release_date = event.release_date.date
                    if release_date == today and market_time == 'pre-market' and event.release_time == 2:
                        # current time is not after-market, skip
                        continue

                    event.ticker = event.ticker.upper()

                    need_process_events.append(event)

                if len(need_process_events) == 0:
                    return

                # ticker_mapper = self.get_cik_mapper()
                ticker_mapper = get_ticker_to_cik_map()
                ticker_mapper2 = get_ticker_to_cik_map_from_stock_mapper()
                for event in need_process_events:
                    cik1 = ticker_mapper.get(event.ticker, '')
                    cik2 = ticker_mapper2.get(event.ticker, '')
                    if cik1 == '' and cik2 == '':
                        logger.warning(f"CIK not found for ticker {event.ticker}, skip")
                        # Mark as processed so it won't be retried
                        event.is_filing_processed = 1
                        session.commit()
                        logger.info(f"Updated earnings event {event.id} for {event.ticker} as processed")
                        continue
                    cik = cik1 if cik1 != '' else cik2
                    filings = get_filings_by_cik(cik, min_date=today)
                    if len(filings) == 0:
                        logger.info(f"No new filings found for {event.ticker} (CIK: {cik}), retry next time")
                        continue
                    self.save_filings(event.ticker, cik, filings, event, session)

            except Exception as e:
                logger.error(f"Error fetching today's earnings events: {str(e)}")
                logger.error(traceback.format_exc())
                session.rollback()
                return []

    def save_filings(self, ticker, cik, filings, earnings_event, session):
        filing_processed = False
        for filing in filings:
                accession_number = filing['accession_number']
                filing_type = filing['form']
                filing_date = filing['filing_date']
                report_date = filing.get('report_date', filing_date)
                index_url = filing.get('index_url')
                html_url = filing.get('html_url')
                txt_url = filing.get('txt_url')

                existing_filing = session.query(SECFiling).filter(
                    SECFiling.accession_number == accession_number
                ).first()

                fiscal_year = None
                fiscal_quarter = None
                if existing_filing and existing_filing.ticker == ticker:
                    logger.info(f"Filing {accession_number} already exists, skip")
                    self.skip_count += 1
                    filing_processed = True
                    continue
                elif existing_filing:
                    # same accession number but different ticker, add the ticker, use same fiscal year and quarter
                    fiscal_year = existing_filing.fiscal_year
                    fiscal_quarter = existing_filing.fiscal_quarter
                else:
                    fiscal_year, fiscal_quarter, report_date = get_filing_fiscal_year_and_quarter(ticker, accession_number, filing_type, report_date)

                # Default value for is_financial_data
                is_financial_data = False


                if filing_type == '10-Q' or filing_type == '6-K' or filing_type == '8-K':
                    is_financial_data = self.check_contains_financial_data_by_gpt(html_url)
                    self.gpt_search_count += 1
                elif filing_type == '10-K' or filing_type == '20-F':
                    is_financial_data = True

                new_filing = SECFiling(
                    ticker=ticker,
                    cik=cik,
                    accession_number=accession_number,
                    filing_type=filing_type,
                    filing_date=datetime.strptime(filing_date, '%Y-%m-%d').date(),
                    report_date=datetime.strptime(report_date, '%Y-%m-%d').date(),
                    fiscal_year=fiscal_year,
                    fiscal_quarter=fiscal_quarter,
                    url=txt_url,
                    html_url=html_url,
                    index_url=index_url,
                    is_processed=True,
                    is_financial_data=is_financial_data,
                )
                session.add(new_filing)
                session.commit()
                self.create_count += 1
                filing_processed = True

        # Update the earnings event as processed if any filing was processed
        if filing_processed:
            earnings_event.is_filing_processed = 1
            session.commit()
            logger.info(f"Updated earnings event {earnings_event.id} for {ticker} as processed")

    def check_contains_financial_data_by_gpt(self, html_url):
        system_prompt = f"""You are given a URL to an SEC filing text document."""
        user_prompt = f"""
Please check the content of this SEC filing URL and also use any recent web search results to determine if it contains or announces any official financial statements, such as quarterly earnings reports, annual reports, 10-Q, 10-K filings, or any other financial disclosures.
The URL is: {html_url}
Look specifically for keywords like "financial statements," "earnings," "quarterly report," "annual report," "Form 10-Q," "Form 10-K," or "operating results."
Please answer "true" if yes, otherwise "false."
Return the result strictly as a JSON object with the following format: {{"financial": true or false}}

IMPORTANT:
- Output ONLY the JSON object, as plain text.
- Do NOT include code block markers like triple backticks.
- Do NOT use Markdown formatting.
- Do NOT include any explanation, commentary, or surrounding text.
"""
        content = do_gpt_query(user_prompt, system_prompt, enable_web_search=True)
        logger.info(f"GPT response for financial data check: {content}")
        try:
            cleaned_content = re.sub(r"^```(?:json)?\s*|\s*```$", "", content.strip())
            result = json.loads(cleaned_content)
            return result.get("financial", False)
        except Exception as e:
            return False

    def get_text_from_url(self, text_url, ignore_links=True, ignore_images=True,
                         ignore_tables=False, body_width=0, timeout=30):
        """
        Fetch text content from a URL and remove any HTML/XML tags.

        Args:
            text_url: URL to fetch text from
            ignore_links: Whether to ignore links in the HTML (default: True)
            ignore_images: Whether to ignore images in the HTML (default: True)
            ignore_tables: Whether to ignore tables in the HTML (default: False)
            body_width: Width to wrap text at, 0 for no wrapping (default: 0)
            timeout: Request timeout in seconds (default: 30)

        Returns:
            str: Plain text content with HTML/XML tags removed
        """
        if not text_url:
            logger.warning("No URL provided to get_text_from_url")
            return ""

        try:
            headers = {
                "User-Agent": "AddxAI/1.0",
                "Content-Type": "text/html",
            }

            logger.info(f"Fetching content from URL: {text_url}")
            response = requests.get(text_url, headers=headers, timeout=timeout)
            response.raise_for_status()

            # Use html2text to convert HTML to plain text
            h = html2text.HTML2Text()
            h.ignore_links = ignore_links
            h.ignore_images = ignore_images
            h.ignore_tables = ignore_tables
            h.body_width = body_width

            # Convert HTML to plain text
            text = h.handle(response.text)

            # Remove any remaining XML tags with regex
            text = re.sub(r'<[^>]+>', '', text)
            return text.strip()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching content from URL {text_url}: {str(e)}")
            return ""
        except Exception as e:
            logger.error(f"Error processing content from URL {text_url}: {str(e)}")
            return ""

    def run(self):
        """Main job execution method."""

        try:
            # Create job run record and store its ID
            with get_db_session() as session:
                job_run = JobRun.create_job_run(session, "real_time_filing")
                session.flush()  # Ensure we have the ID
                self.job_run_id = job_run.id
                session.commit()
                logger.info(f"Created job run record with ID: {self.job_run_id}")
            # Get today's earnings events
            self.process_todays_earnings_events()

            with get_db_session() as session:
                job_run = session.query(JobRun).get(self.job_run_id)
                job_run.complete(session, 0)
            logger.info(f"Completed processing for {self.create_count} companies, use gpt search {self.gpt_search_count}, skip {self.skip_count}")

        except Exception as e:
            logger.error(f"Error in real-time filing job: {str(e)}")
            logger.error(traceback.format_exc())
            # Mark job as failed using a fresh session
            if self.job_run_id:
                with get_db_session() as session:
                    job_run = session.query(JobRun).get(self.job_run_id)
                    if job_run:
                        job_run.fail(session, str(e), 0)

if __name__ == "__main__":
    from config.settings import settings
    job = RealTimeFilingJob(settings=settings)
    job.run()
