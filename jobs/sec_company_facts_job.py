#!/usr/bin/env python3
"""
Job to fetch SEC XBRL company facts data for US listed stocks.
This job retrieves structured financial data from the SEC's XBRL API and stores it in a MySQL database.
Only the most important financial metrics defined in config/sec_concepts.py are stored.
"""

import os
import sys
import time
import argparse
import json
import requests
import logging
import datetime
import concurrent.futures
from typing import List, Dict, Any, Optional, Tuple, Set
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
from ratelimit import limits, sleep_and_retry

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings
from config.sec_concepts import ALL_CONCEPTS_TO_STORE, COMMON_FINANCIAL_CONCEPTS, DEI_CONCEPTS
from utils.logging import logger
from jobs.db.mysql_client import get_db_session
from jobs.db.migrations.migrations import apply_migrations  # Add this import
from jobs.db.schema import (
    AllTicker, 
    JobRun, 
    SECCompanyFactMetadata, 
    SECCompanyFact,
    SP500Ticker,
    Nasdaq100Ticker,
    NasdaqCompositeTicker,
    DowJonesTicker,
    Russell2000Ticker
)

# Constants
SEC_API_CALLS_PER_SECOND = 10  # SEC API rate limit

@sleep_and_retry
@limits(calls=SEC_API_CALLS_PER_SECOND, period=1)
def make_sec_api_request(url: str, timeout: int = 30) -> requests.Response:
    """
    Make a rate-limited request to the SEC API with timeout.
    
    Args:
        url: URL to request
        timeout: Request timeout in seconds

    Returns:
        Response object
    """
    headers = {
        "User-Agent": f"{settings.SEC_API_ORGANIZATION} {settings.SEC_API_EMAIL}",
        "Content-Type": "application/json",
    }
    
    logger.info(f"Making SEC API request to: {url} with timeout {timeout}s")
    start_time = time.time()

    try:
        response = requests.get(url, headers=headers, timeout=timeout)
        elapsed_time = time.time() - start_time
        logger.info(f"SEC API request completed in {elapsed_time:.2f}s: {url}")
        response.raise_for_status()
        return response
    except Exception as e:
        elapsed_time = time.time() - start_time
        logger.error(f"SEC API request failed after {elapsed_time:.2f}s: {url}, Error: {str(e)}")
        raise

# Global cache for ticker to CIK mapping
_TICKER_TO_CIK_CACHE = {}

def fetch_all_ciks() -> Dict[str, str]:
    """
    Fetch all ticker to CIK mappings from the SEC API.
    This is called once at the beginning of the job run.

    Returns:
        Dictionary mapping ticker symbols (uppercase) to CIK numbers
    """
    global _TICKER_TO_CIK_CACHE
    
    if len(_TICKER_TO_CIK_CACHE) > 0:
        return _TICKER_TO_CIK_CACHE
        
    try:
        logger.info("Fetching all ticker to CIK mappings from SEC API")
        start_time = time.time()
        url = f"{settings.SEC_CIK_LOOKUP_URL}"
        response = make_sec_api_request(url, timeout=30)

        # Parse the response
        cik_data = response.json()

        # Create a mapping of ticker to CIK
        ticker_to_cik = {}
        for entry in cik_data.values():
            ticker = entry.get("ticker")
            if ticker:
                # Remove leading zeros from CIK
                cik = str(int(entry.get("cik_str")))
                ticker_to_cik[ticker] = cik

        elapsed_time = time.time() - start_time
        logger.info(f"Successfully fetched {len(ticker_to_cik)} ticker to CIK mappings in {elapsed_time:.2f}s")
        _TICKER_TO_CIK_CACHE = ticker_to_cik
        return ticker_to_cik

    except requests.exceptions.Timeout:
        logger.error("Timeout fetching ticker to CIK mappings - SEC API did not respond within timeout period")
        return {}
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching ticker to CIK mappings: {str(e)}")
        return {}
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding CIK JSON: {str(e)}")
        return {}

def get_cik_for_ticker(ticker: str) -> Optional[str]:
    """
    Get the CIK number for a ticker symbol.
    
    Args:
        ticker: Ticker symbol
        
    Returns:
        CIK number or None if not found
    """
    try:
        # First check if we have the CIK in the database
        with get_db_session() as session:
            result = session.execute(
                text("SELECT cik FROM sec_company_fact_metadata WHERE ticker = :ticker"),
                {"ticker": ticker}
            ).fetchone()
            
            if result:
                logger.info(f"Found CIK for {ticker} in database: {result[0]}")
                return result[0]
        
        # Then check if we have it in our in-memory cache
        all_tickers = fetch_all_ciks()
        if ticker.upper() in all_tickers:
            cik = all_tickers[ticker.upper()]
            logger.info(f"Found CIK for {ticker} in cache: {cik}")
            return cik
        
        # If not found in cache or database, log a warning
        logger.warning(f"CIK not found for ticker {ticker} in database or cache")
        return None
    
    except Exception as e:
        logger.error(f"Error getting CIK for ticker {ticker}: {str(e)}")
        return None

def fetch_company_facts(cik: str, ticker: str) -> Optional[Dict[str, Any]]:
    """
    Fetch company facts for a given CIK.
    
    Args:
        cik: CIK number
        ticker: Ticker symbol
        
    Returns:
        Company facts data or None if not found
    """
    # Pad CIK with leading zeros to 10 digits
    cik_padded = cik.zfill(10)
    
    url = f"{settings.SEC_XBRL_API_URL}/CIK{cik_padded}.json"
    
    logger.info(f"Fetching company facts for {ticker} (CIK {cik})")
    try:
        start_time = time.time()
        response = make_sec_api_request(url, timeout=60)  # Increased timeout for company facts
        elapsed_time = time.time() - start_time
        logger.info(f"Successfully fetched company facts for {ticker} in {elapsed_time:.2f}s")
        
        return response.json()
    except requests.exceptions.Timeout:
        logger.error(f"Timeout fetching company facts for {ticker} (CIK {cik}) - SEC API did not respond within timeout period")
        return None
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching company facts for {ticker} (CIK {cik}): {str(e)}")
        return None
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON for {ticker} (CIK {cik}): {str(e)}")
        return None

def process_company_facts(
    ticker: str, 
    cik: str, 
    company_facts: Dict[str, Any], 
    session
) -> Tuple[int, int]:
    """
    Process company facts and store in the database.
    Only stores the concepts defined in ALL_CONCEPTS_TO_STORE.
    
    Args:
        ticker: Ticker symbol
        cik: CIK number
        company_facts: Company facts data
        session: Database session
        
    Returns:
        Tuple of (number of facts processed, number of facts stored)
    """
    logger.info(f"Starting to process company facts for {ticker}")
    start_time = time.time()
    if not company_facts or 'facts' not in company_facts:
        logger.warning(f"No facts found for {ticker} (CIK {cik})")
        return 0, 0
    
    # Get or create metadata record
    metadata = session.query(SECCompanyFactMetadata).filter_by(ticker=ticker).first()
    
    if not metadata:
        metadata = SECCompanyFactMetadata(
            ticker=ticker,
            cik=cik,
            entity_name=company_facts.get('entityName'),
            version=company_facts.get('version'),
            taxonomy_version=company_facts.get('taxonomyVersion'),
            is_processed=False
        )
        session.add(metadata)
        session.flush()  # Flush to get the ID but don't commit yet
    else:
        # Update metadata
        metadata.entity_name = company_facts.get('entityName')
        metadata.version = company_facts.get('version')
        metadata.taxonomy_version = company_facts.get('taxonomyVersion')
        metadata.last_updated = datetime.datetime.now()
        metadata.is_processed = False

    # Fetch all existing facts for this metadata in a single query
    existing_facts = session.query(SECCompanyFact).filter_by(metadata_id=metadata.id).all()

    # Create a lookup dictionary for fast existence checks
    # Key format: (taxonomy, concept, end_date, form)
    existing_facts_dict = {}
    for fact in existing_facts:
        key = (fact.taxonomy, fact.concept, fact.end_date, fact.form)
        existing_facts_dict[key] = fact
    
    facts_processed = 0
    facts_stored = 0
    facts_to_update = []
    facts_to_add = []

    # Pre-filter taxonomies and concepts we care about
    relevant_taxonomies = ['us-gaap', 'dei']

    # Process facts
    for taxonomy, taxonomy_facts in company_facts.get('facts', {}).items():
        # Only process us-gaap and dei taxonomies
        if taxonomy not in relevant_taxonomies:
            continue
        
        for concept, concept_data in taxonomy_facts.items():
            facts_processed += 1
            
            # Skip concepts not in our list of important concepts to store
            if (taxonomy == 'us-gaap' and concept not in COMMON_FINANCIAL_CONCEPTS) or \
               (taxonomy == 'dei' and concept not in DEI_CONCEPTS):
                continue
            
            # Process units
            for unit, unit_data in concept_data.get('units', {}).items():
                for fact in unit_data:
                    # Skip facts without end date
                    if 'end' not in fact:
                        continue
                    
                    # Parse dates
                    end_date = datetime.datetime.strptime(fact['end'], '%Y-%m-%d').date()
                    start_date = None
                    if 'start' in fact:
                        start_date = datetime.datetime.strptime(fact['start'], '%Y-%m-%d').date()
                    
                    # Parse filing date
                    filing_date = None
                    if 'filed' in fact:
                        filing_date = datetime.datetime.strptime(fact['filed'], '%Y-%m-%d').date()
                    
                    # Get value
                    value = str(fact.get('val', ''))
                    value_numeric = None
                    try:
                        value_numeric = float(value)
                    except (ValueError, TypeError):
                        pass
                    
                    # Check if fact already exists using our lookup dictionary
                    key = (taxonomy, concept, end_date, fact.get('form'))
                    existing_fact = existing_facts_dict.get(key)
                    
                    if existing_fact:
                        # Update existing fact
                        existing_fact.label = concept_data.get('label')
                        existing_fact.description = concept_data.get('description')
                        existing_fact.value = value
                        existing_fact.value_numeric = value_numeric
                        existing_fact.unit = unit
                        existing_fact.filing_date = filing_date
                        existing_fact.start_date = start_date
                        existing_fact.fiscal_year = fact.get('fy')
                        existing_fact.fiscal_period = fact.get('fp')
                        existing_fact.frame = fact.get('frame')
                        existing_fact.accession_number = fact.get('accn')
                        existing_fact.filing_url = fact.get('filing_url')
                        existing_fact.last_updated = datetime.datetime.now()
                        facts_to_update.append(existing_fact)
                    else:
                        # Create new fact
                        new_fact = SECCompanyFact(
                            metadata_id=metadata.id,
                            taxonomy=taxonomy,
                            concept=concept,
                            label=concept_data.get('label'),
                            description=concept_data.get('description'),
                            value=value,
                            value_numeric=value_numeric,
                            unit=unit,
                            filing_date=filing_date,
                            end_date=end_date,
                            start_date=start_date,
                            fiscal_year=fact.get('fy'),
                            fiscal_period=fact.get('fp'),
                            form=fact.get('form'),
                            frame=fact.get('frame'),
                            accession_number=fact.get('accn'),
                            filing_url=fact.get('filing_url')
                        )
                        facts_to_add.append(new_fact)
                    
                    facts_stored += 1
    
    # Bulk add new facts
    if facts_to_add:
        session.bulk_save_objects(facts_to_add)

    # Mark metadata as processed
    metadata.is_processed = True
    metadata.last_updated = datetime.datetime.now()

    # Commit all changes in a single transaction
    session.commit()
    
    elapsed_time = time.time() - start_time
    logger.info(f"Completed processing company facts for {ticker} in {elapsed_time:.2f}s: {facts_processed} facts processed, {facts_stored} facts stored")

    return facts_processed, facts_stored

def get_tickers_from_index(index_name: str, session) -> List[str]:
    """
    Get tickers from a specific index.
    
    Args:
        index_name: Index name (sp500, nasdaq100, nasdaq, dow, russell2000)
        session: Database session
        
    Returns:
        List of tickers
    """
    if index_name == 'sp500':
        return [row[0] for row in session.query(SP500Ticker.ticker).all()]
    elif index_name == 'nasdaq100':
        return [row[0] for row in session.query(Nasdaq100Ticker.ticker).all()]
    elif index_name == 'nasdaq':
        return [row[0] for row in session.query(NasdaqCompositeTicker.ticker).all()]
    elif index_name == 'dow':
        return [row[0] for row in session.query(DowJonesTicker.ticker).all()]
    elif index_name == 'russell2000':
        return [row[0] for row in session.query(Russell2000Ticker.ticker).all()]
    else:
        logger.error(f"Unknown index: {index_name}")
        return []

def get_all_tickers(session) -> List[str]:
    """
    Get all tickers from the stocks table.
    
    Args:
        session: Database session
        
    Returns:
        List of tickers
    """
    return [row[0] for row in session.query(AllTicker.ticker).all()]

def get_all_tickers_without_facts_processed(session) -> List[str]:
    """
    Get all tickers that haven't had their facts processed yet.
    This returns tickers that either don't have a metadata record or
    have one with is_processed=False.
    
    Args:
        session: Database session
        
    Returns:
        List of tickers that need processing
    """
    result = session.query(AllTicker.ticker)\
        .filter((AllTicker.is_facts_processed == 0))\
        .all()
    
    return [row[0] for row in result]

# Create a table to track job progress if it doesn't exist
def create_progress_table(session):
    """Create the job progress tracking table if it doesn't exist."""
    try:
        session.execute(text("""
            CREATE TABLE IF NOT EXISTS sec_job_progress (
                job_name VARCHAR(255) PRIMARY KEY,
                ticker VARCHAR(20),
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """))
        session.commit()
        logger.info("Created or verified sec_job_progress table")
    except SQLAlchemyError as e:
        logger.error(f"Error creating progress table: {str(e)}")
        session.rollback()

def get_last_processed_ticker(session) -> Optional[str]:
    """Get the last successfully processed ticker from the database."""
    try:
        result = session.execute(
            text("SELECT ticker FROM sec_job_progress WHERE job_name = 'sec_company_facts_job' ORDER BY updated_at DESC LIMIT 1")
        ).fetchone()
        if result:
            logger.info(f"Found checkpoint - last processed ticker: {result[0]}")
            return result[0]
        logger.info("No checkpoint found, starting from the beginning")
        return None
    except Exception as e:
        logger.error(f"Error retrieving last processed ticker: {str(e)}")
        return None

def update_last_processed_ticker(session, ticker: str) -> None:
    """Update the last processed ticker in the database."""
    try:
        session.execute(
            text("INSERT INTO sec_job_progress (job_name, ticker, updated_at) VALUES (:job_name, :ticker, NOW()) "
                 "ON DUPLICATE KEY UPDATE ticker = :ticker, updated_at = NOW()"),
            {"job_name": "sec_company_facts_job", "ticker": ticker}
        )
        session.commit()
        logger.info(f"Updated checkpoint - last processed ticker: {ticker}")
    except Exception as e:
        logger.error(f"Error updating last processed ticker: {str(e)}")
        session.rollback()

def process_single_ticker(ticker: str) -> Tuple[int, int, bool]:
    """
    Process a single ticker, handling its own database session.
    
    Args:
        ticker: Ticker symbol to process
        
    Returns:
        Tuple of (facts_processed, facts_stored, success)
    """
    ticker_start_time = time.time()
    logger.info(f"Processing {ticker}")
    
    # Create a new session for this thread
    with get_db_session() as session:
        try:
            # Get CIK for ticker
            cik = get_cik_for_ticker(ticker)
            
            if not cik:
                logger.warning(f"CIK not found for {ticker}, skipping")
                return 0, 0, False
            
            # Fetch company facts
            company_facts = fetch_company_facts(cik, ticker)
            
            if not company_facts:
                logger.warning(f"No company facts found for {ticker} (CIK {cik}), skipping")
                return 0, 0, False
            
            # Process company facts
            facts_processed, facts_stored = process_company_facts(ticker, cik, company_facts, session)
            
            # Update the is_facts_processed flag for this ticker in the all_ticker table
            session.execute(
                text("UPDATE all_tickers SET is_facts_processed = 1 WHERE ticker = :ticker"),
                {"ticker": ticker}
            )
            session.commit()
            logger.info(f"Marked {ticker} as having facts processed")
            
            logger.info(f"Processed {facts_processed} facts, stored {facts_stored} facts for {ticker}")
            
            ticker_elapsed_time = time.time() - ticker_start_time
            logger.info(f"Completed processing {ticker} in {ticker_elapsed_time:.2f}s")
            
            return facts_processed, facts_stored, True
            
        except Exception as e:
            ticker_elapsed_time = time.time() - ticker_start_time
            logger.error(f"Error processing {ticker} after {ticker_elapsed_time:.2f}s: {str(e)}")
            return 0, 0, False

def _init_database():
    """
    Initialize the database.
    """
    logger.info("Initializing database")
    try:
        # Apply migrations instead of creating tables directly
        apply_migrations()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise

def run_job(tickers: Optional[List[str]] = None, index: Optional[str] = None, max_workers: int = 50) -> bool:
    """
    Run the SEC company facts job.
    
    Args:
        tickers: List of tickers to process (optional)
        index: Index to process (optional)
        max_workers: Maximum number of concurrent workers
        
    Returns:
        True if successful, False otherwise
    """
    # Adjust max_workers to avoid database connection pool exhaustion
    # The pool size is defined in settings.py as DB_POOL_SIZE
    from config.settings import settings

    _init_database()
    
    # Calculate safe worker count based on pool size
    # Leave some connections for other operations
    pool_size = settings.DB_POOL_SIZE
    safe_workers = min(max_workers, max(1, pool_size // 2))
    
    if safe_workers < max_workers:
        logger.warning(f"Reducing max_workers from {max_workers} to {safe_workers} to prevent database connection pool exhaustion")
        max_workers = safe_workers
        
    logger.info(f"Starting SEC company facts job with {max_workers} concurrent workers")
    job_start_time = time.time()

    with get_db_session() as session:
        # Create job run record
        job_run = JobRun.create_job_run(session, "sec_company_facts_job")
        
        try:
            # Get tickers to process
            if tickers:
                logger.info(f"Processing {len(tickers)} specified tickers")
            elif index:
                tickers = get_tickers_from_index(index, session)
                logger.info(f"Processing {len(tickers)} tickers from {index} index")
            else:
                tickers = get_all_tickers_without_facts_processed(session)
                logger.info(f"Processing all {len(tickers)} tickers")
            
            total_facts_processed = 0
            total_facts_stored = 0
            tickers_processed = 0
            tickers_failed = 0
            
            # Create progress tracking table if it doesn't exist
            create_progress_table(session)

            # Check if we have a checkpoint to resume from
            last_processed_ticker = None
            if not tickers and not index:  # Only use checkpointing for full runs
                last_processed_ticker = get_last_processed_ticker(session)
                if last_processed_ticker:
                    # Find the index of the last processed ticker
                    try:
                        start_index = tickers.index(last_processed_ticker) + 1
                        if start_index < len(tickers):
                            logger.info(f"Resuming from ticker {tickers[start_index]} (after {last_processed_ticker})")
                            tickers = tickers[start_index:]
                        else:
                            logger.info(f"All tickers already processed (last was {last_processed_ticker})")
                    except ValueError:
                        logger.warning(f"Last processed ticker {last_processed_ticker} not found in current ticker list")

            # Process tickers concurrently
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all ticker processing tasks
                future_to_ticker = {
                    executor.submit(process_single_ticker, ticker): ticker 
                    for ticker in tickers
                }
                
                # Process results as they complete
                for future in concurrent.futures.as_completed(future_to_ticker):
                    ticker = future_to_ticker[future]
                    try:
                        facts_processed, facts_stored, success = future.result()
                        total_facts_processed += facts_processed
                        total_facts_stored += facts_stored
                        
                        if success:
                            tickers_processed += 1
                            # Update checkpoint for successfully processed tickers
                            if not tickers and not index:  # Only use checkpointing for full runs
                                with get_db_session() as checkpoint_session:
                                    update_last_processed_ticker(checkpoint_session, ticker)
                        else:
                            tickers_failed += 1
                    except Exception as exc:
                        logger.error(f"Ticker {ticker} generated an exception: {exc}")
                        tickers_failed += 1
            
            # Complete job run
            job_run.complete(session, total_facts_stored)
            
            job_elapsed_time = time.time() - job_start_time
            logger.info(f"SEC company facts job completed successfully in {job_elapsed_time:.2f}s")
            logger.info(f"Processed {tickers_processed} tickers, failed {tickers_failed} tickers")
            logger.info(f"Processed {total_facts_processed} facts, stored {total_facts_stored} facts")
            
            return True
        
        except Exception as e:
            logger.error(f"Error running SEC company facts job: {str(e)}")
            job_run.fail(session, str(e))
            return False

def main():
    """
    Main entry point.
    """
    parser = argparse.ArgumentParser(description='Fetch SEC XBRL company facts data for US listed stocks')
    parser.add_argument('--tickers', type=str, help='Comma-separated list of tickers to process')
    parser.add_argument('--index', type=str, choices=['sp500', 'nasdaq100', 'nasdaq', 'dow', 'russell2000'], 
                        help='Index to process')
    parser.add_argument('--workers', type=int, default=5, help='Number of concurrent workers')
    
    args = parser.parse_args()
    
    tickers = None
    if args.tickers:
        tickers = [ticker.strip() for ticker in args.tickers.split(',')]
    
    success = run_job(tickers=tickers, index=args.index, max_workers=args.workers)
    
    if not success:
        sys.exit(1)

if __name__ == '__main__':
    main()
