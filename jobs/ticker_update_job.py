import json
import os
import sys
import time
import traceback
import concurrent.futures
import pandas as pd
from typing import Dict, List, Tuple, Optional

from jobs.sec_company_facts_job import fetch_company_facts
from utils.logging import setup_logging

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from jobs.db.mysql_client import get_db_session
from jobs.db.schema import JobRun, SP500Ticker, Nasdaq100Ticker, NasdaqCompositeTicker, DowJonesTicker, Russell2000Ticker, AllTicker
from jobs.db.migrations.migrations import apply_migrations  # Add this import
from utils.logo_api import get_logo_url
from utils.image_utils import download_logo
from utils.exchange_detector import detect_exchange
from utils.ticker_utils import strip_leading_zeros, generate_yf_symbol
from config.settings import settings

# Configure logging
logger = setup_logging(enable_colors=False)

# Get GCS bucket name from settings
GCS_BUCKET = os.getenv('GCS_LOGO_BUCKET', 'images.dev.addxgo.io/tickers')

class TickerUpdateJob:
    def __init__(self, use_tushare: bool = False, skip_update_logo: bool = False):
        self.processed_count = 0
        self.error_count = 0
        self.batch_counter = 0
        self.use_tushare = use_tushare
        self.skip_update_logo = skip_update_logo
        # Initialize database
        self._init_database()

    def _init_database(self):
        """
        Initialize the database.
        """
        logger.info("Initializing database")
        try:
            # Apply migrations instead of creating tables directly
            apply_migrations()
            logger.info("Database initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise

    def run(self):
        """
        Run the job.
        """
        logger.info("Starting ticker update job")
        start_time = time.time()

        try:
            # Create job run record and store its ID
            with get_db_session() as session:
                job_run = JobRun.create_job_run(session, "ticker_update_job")
                session.flush()  # Ensure we have the ID
                self.job_run_id = job_run.id
                session.commit()
                logger.info(f"Created job run record with ID: {self.job_run_id}")

            if self.use_tushare:
                self._get_us_tickers_from_tushare()
            else:
                self._get_tickers_from_json()

            # Mark job as completed using a fresh session
            with get_db_session() as session:
                job_run = session.query(JobRun).get(self.job_run_id)
                job_run.complete(session, self.processed_count)

            end_time = time.time()
            logger.info(f"Job completed successfully in {end_time - start_time:.2f} seconds")
            return True
        except Exception as e:
            logger.error(f"Error running job: {str(e)}")
            logger.error(traceback.format_exc())

            # Mark job as failed using a fresh session
            if self.job_run_id:
                with get_db_session() as session:
                    job_run = session.query(JobRun).get(self.job_run_id)
                    if job_run:
                        job_run.fail(session, str(e), self.processed_count)

            return False

    def _get_tickers_from_json(self):
        # Get the project root directory
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        nyse_path = os.path.join(project_root, 'jobs', 'tickers', 'nyse.json')
        nasdaq_path = os.path.join(project_root, 'jobs', 'tickers', 'nasdaq.json')
        amex_path = os.path.join(project_root, 'jobs', 'tickers', 'amex.json')
        cboe_path = os.path.join(project_root, 'jobs', 'tickers', 'cboe.json')
        idx_sp_500_path = os.path.join(project_root, 'jobs', 'tickers', 'idx_sp_500.json')
        idx_nasdaq_100_path = os.path.join(project_root, 'jobs', 'tickers', 'idx_nasdaq_100.json')
        idx_nasdaq_composite_path = os.path.join(project_root, 'jobs', 'tickers', 'idx_nasdaq_composite.json')
        idx_dow_jones_path = os.path.join(project_root, 'jobs', 'tickers', 'idx_dow_jones.json')
        idx_russell_2000_path = os.path.join(project_root, 'jobs', 'tickers', 'idx_russell_2000.json')
        prev_path = os.path.join(project_root, 'jobs', 'tickers', 'prev.json')

        def extract_tickers(json_data):
            """Extract ticker symbols and company names from the JSON data format [["TICKER","Company Name"]]"""
            # Return both the full data (ticker, company_name) and just the ticker symbols
            full_data = [(item[0], item[1]) for item in json_data]
            ticker_only = [item[0] for item in json_data]
            return full_data, ticker_only

        def extract_prev_tickers(json_data):
            m = {}
            for item in json_data:
                old_ticker, new_ticker = item[0], item[1]
                m[new_ticker] = old_ticker
            return m

        try:
            # Load all ticker lists
            with open(nyse_path, 'r', encoding='utf-8') as file:
                nyse_tickers_data, nyse_tickers = extract_tickers(json.load(file))
            with open(nasdaq_path, 'r', encoding='utf-8') as file:
                nasdaq_tickers_data, nasdaq_tickers = extract_tickers(json.load(file))
            with open(amex_path, 'r', encoding='utf-8') as file:
                amex_tickers_data, amex_tickers = extract_tickers(json.load(file))
            with open(cboe_path, 'r', encoding='utf-8') as file:
                cboe_tickers_data, cboe_tickers = extract_tickers(json.load(file))
            with open(idx_sp_500_path, 'r', encoding='utf-8') as file:
                sp500_tickers_data, sp500_tickers = extract_tickers(json.load(file))
            with open(idx_nasdaq_100_path, 'r', encoding='utf-8') as file:
                nasdaq100_tickers_data, nasdaq100_tickers = extract_tickers(json.load(file))
            with open(idx_nasdaq_composite_path, 'r', encoding='utf-8') as file:
                nasdaq_composite_tickers_data, nasdaq_composite_tickers = extract_tickers(json.load(file))
            with open(idx_dow_jones_path, 'r', encoding='utf-8') as file:
                dow_jones_tickers_data, dow_jones_tickers = extract_tickers(json.load(file))
            with open(idx_russell_2000_path, 'r', encoding='utf-8') as file:
                russell_2000_tickers_data, russell_2000_tickers = extract_tickers(json.load(file))
            with open(prev_path, 'r', encoding='utf-8') as file:
                prev_tickers = extract_prev_tickers(json.load(file))

            # Combine all ticker data (with company names)
            all_ticker_data = (
                nyse_tickers_data + nasdaq_tickers_data + amex_tickers_data + cboe_tickers_data +
                sp500_tickers_data + nasdaq100_tickers_data + nasdaq_composite_tickers_data +
                dow_jones_tickers_data + russell_2000_tickers_data
            )

            # Create a dictionary to store unique tickers with their company names
            # and track which indexes they belong to
            ticker_dict = {}
            for ticker, company_name in all_ticker_data:
                if ticker not in ticker_dict:
                    ticker_dict[ticker] = {
                        'name': company_name,
                        'country': 'US',
                        'is_sp_500': False,
                        'is_nasdap_100': False,
                        'is_nasdaq_composite': False,
                        'is_dow_jones': False,
                        'is_russell_2000': False,
                        'exchange': ''  # Initialize exchange field
                    }
                else:
                    # Update company name if we have one
                    if company_name:
                        ticker_dict[ticker]['name'] = company_name

            # Mark exchanges for each ticker - with breaks for efficiency
            for ticker in nyse_tickers:
                if ticker in ticker_dict:
                    ticker_dict[ticker]['exchange'] = 'NYSE'

            for ticker in nasdaq_tickers:
                if ticker in ticker_dict:
                    ticker_dict[ticker]['exchange'] = 'NASDAQ'

            for ticker in amex_tickers:
                if ticker in ticker_dict:
                    ticker_dict[ticker]['exchange'] = 'AMEX'

            for ticker in cboe_tickers:
                if ticker in ticker_dict:
                    ticker_dict[ticker]['exchange'] = 'CBOE'

            # Mark tickers that belong to specific indexes - with breaks for efficiency
            for ticker in sp500_tickers:
                if ticker in ticker_dict:
                    ticker_dict[ticker]['is_sp_500'] = True

            for ticker in nasdaq100_tickers:
                if ticker in ticker_dict:
                    ticker_dict[ticker]['is_nasdap_100'] = True

            for ticker in nasdaq_composite_tickers:
                if ticker in ticker_dict:
                    ticker_dict[ticker]['is_nasdaq_composite'] = True

            for ticker in dow_jones_tickers:
                if ticker in ticker_dict:
                    ticker_dict[ticker]['is_dow_jones'] = True

            for ticker in russell_2000_tickers:
                if ticker in ticker_dict:
                    ticker_dict[ticker]['is_russell_2000'] = True

            # Sort the tickers alphabetically
            sorted_tickers = sorted(ticker_dict.keys())

            from sec_cik_mapper import StockMapper
            mapper = StockMapper()

            # Get existing tickers with logos
            existing_logos = {}
            with get_db_session() as session:
                for ticker_obj in session.query(AllTicker.ticker, AllTicker.logo).all():
                    if ticker_obj.logo:  # If logo exists and is not None/empty
                        existing_logos[ticker_obj.ticker] = ticker_obj.logo

            # Prepare data for concurrent processing
            ticker_info_list = []
            for ticker in sorted_tickers:
                data = ticker_dict[ticker]
                if '.' in ticker:
                    ticker = ticker.replace('.', '-')
                if '/' in ticker:
                    ticker = ticker.replace('/', '-')
                name = mapper.ticker_to_company_name.get(ticker)
                cik = mapper.ticker_to_cik.get(ticker)
                exchange = mapper.ticker_to_exchange.get(ticker, '').upper()
                if not name and data['name'] == '':
                    logger.warning(f"Company name not found for ticker {ticker}, skip")
                    continue
                if not cik:
                    logger.warning(f"Cik not found for ticker {ticker}, skip")
                    continue

                if data['name'] == '':
                    data['name'] = name
                if data['exchange'] != exchange:
                    pass
                if data['exchange'] == '':
                    data['exchange'] = exchange

                # Always get logo URL
                logo_url = get_logo_url(ticker, data['name'])
                needs_logo_update = True

                ticker_info_list.append({
                    'ticker': ticker,
                    'yf_symbol': ticker,
                    'cik': cik,
                    'name': data['name'],
                    'country': data['country'],
                    'exchange': data['exchange'],
                    'is_sp_500': data['is_sp_500'],
                    'is_nasdap_100': data['is_nasdap_100'],
                    'is_nasdaq_composite': data['is_nasdaq_composite'],
                    'is_dow_jones': data['is_dow_jones'],
                    'is_russell_2000': data['is_russell_2000'],
                    'logo_url': logo_url,
                    'needs_logo_update': needs_logo_update,
                    # JSON source does not have these fields, set to None
                    'classify': None,
                    'list_date': None,
                    'delist_date': None
                })

            # Process company facts concurrently
            # processed_tickers = self._fetch_company_facts_concurrently(ticker_info_list)

            # Count tickers that need logo updates
            logo_update_count = sum(1 for ticker_info in ticker_info_list if ticker_info.get('needs_logo_update', False))

            logger.info(f"Found {len(ticker_info_list)} unique tickers with company facts")
            logger.info(f"Found {len(existing_logos)} tickers with existing logos")
            logger.info(f"Need to update logos for {logo_update_count} tickers")

            # Save tickers to database
            self._save_tickers_to_db(ticker_info_list)

            self._update_prev_tickers(prev_tickers)

            # Return just the ticker symbols for backward compatibility
            return sorted_tickers

        except FileNotFoundError as e:
            logger.error(f"Error reading ticker files: {str(e)}")
            raise

    def _fetch_company_facts_worker(self, ticker_info: Dict) -> Optional[AllTicker]:
        """Worker function for concurrent fetching of company facts"""
        ticker = ticker_info['ticker']
        cik = ticker_info['cik']

        try:
            company_facts = fetch_company_facts(cik, ticker)
            if not company_facts:
                logger.warning(f"Company facts not found for ticker {ticker}, skip")
                return None

            return AllTicker(
                ticker=ticker,
                name=ticker_info['name'],
                country='US',  # Default to US
                exchange=ticker_info['exchange'],
                is_sp_500=ticker_info['is_sp_500'],
                is_nasdap_100=ticker_info['is_nasdap_100'],
                is_nasdaq_composite=ticker_info['is_nasdaq_composite'],
                is_dow_jones=ticker_info['is_dow_jones'],
                is_russell_2000=ticker_info['is_russell_2000']
            )
        except Exception as e:
            logger.error(f"Error processing ticker {ticker}: {str(e)}")
            return None

    def _fetch_company_facts_concurrently(self, ticker_info_list: List[Dict]) -> List[AllTicker]:
        """Fetch company facts concurrently using ThreadPoolExecutor"""
        processed_tickers = []
        total_tickers = len(ticker_info_list)
        logger.info(f"Starting concurrent processing of {total_tickers} tickers")

        # Fetch existing tickers from the database
        with get_db_session() as session:
            existing_tickers = {ticker.ticker for ticker in session.query(AllTicker.ticker).all()}

        # Filter out tickers that already exist in the database
        ticker_info_list = [info for info in ticker_info_list if info['ticker'] not in existing_tickers]
        logger.info(f"Skipping {total_tickers - len(ticker_info_list)} tickers already in the database")

        # Process in batches to avoid overwhelming the API
        batch_size = 200
        max_workers = 50

        for i in range(0, len(ticker_info_list), batch_size):
            batch = ticker_info_list[i:i+batch_size]
            batch_results = []

            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # Submit all tasks and keep track of futures
                future_to_ticker = {
                    executor.submit(self._fetch_company_facts_worker, ticker_info): ticker_info['ticker']
                    for ticker_info in batch
                }

                # Process completed futures as they come in
                for future in concurrent.futures.as_completed(future_to_ticker):
                    ticker = future_to_ticker[future]
                    try:
                        result = future.result()
                        if result:
                            batch_results.append(result)
                    except Exception as e:
                        logger.error(f"Exception processing ticker {ticker}: {str(e)}")

            processed_tickers.extend(batch_results)
            logger.info(f"Processed batch {i//batch_size + 1} of {(len(ticker_info_list) + batch_size - 1)//batch_size}: "
                       f"{len(batch_results)} tickers with valid company facts")

            # Small delay between batches to be nice to the API
            if i + batch_size < len(ticker_info_list):
                time.sleep(0.5)

        return processed_tickers

    def _save_tickers_to_db(self, ticker_data):
        """
        Save tickers to the database.

        Args:
            ticker_data: List of dictionaries containing ticker information
        """
        logger.info(f"Saving {len(ticker_data)} tickers to database")

        # Remove duplicates from ticker_data first
        seen_tickers = set()
        unique_ticker_data = []
        duplicate_count = 0

        for ticker_info in ticker_data:
            ticker_symbol = ticker_info['ticker']
            if ticker_symbol not in seen_tickers:
                seen_tickers.add(ticker_symbol)
                unique_ticker_data.append(ticker_info)
            else:
                duplicate_count += 1
                logger.debug(f"Skipping duplicate ticker in input data: {ticker_symbol}")

        if duplicate_count > 0:
            logger.warning(f"Found and removed {duplicate_count} duplicate tickers from input data")

        batch_size = 20
        batches = [unique_ticker_data[i:i + batch_size] for i in range(0, len(unique_ticker_data), batch_size)]

        total_saved = 0
        total_updated = 0
        total_logos_processed = 0

        with get_db_session() as session:
            # Get existing tickers for comparison
            existing_tickers = {ticker.ticker: ticker for ticker in session.query(AllTicker).all()}

            for batch in batches:
                batch_saved = 0
                batch_updated = 0

                for ticker_info in batch:
                    ticker_symbol = ticker_info['ticker']

                    # Process logo if available in ticker_info and needs update
                    logo_path = None

                    if ('logo_url' in ticker_info and ticker_info['logo_url'] and
                        ticker_info.get('needs_logo_update', False)):
                        logo_url = ticker_info['logo_url']

                        # Use download_logo function from image_utils.py to download and upload the logo to GCS
                        # If GCS_BUCKET is set, upload to GCS, otherwise file_path is none
                        file_path, _ = download_logo(ticker_symbol, logo_url, '/us')

                        if file_path:
                            # Store the GCS URL or local file path in the database
                            logo_path = file_path
                            total_logos_processed += 1
                            logger.info(f"Downloaded and uploaded logo for ticker {ticker_symbol} to GCS: {file_path}")
                        else:
                            # If download fails, logo_path will be none
                            logger.warning(f"Failed to download logo for ticker {ticker_symbol}")
                    elif 'logo_url' in ticker_info and ticker_info['logo_url']:
                        # If logo doesn't need update, keep the existing logo URL
                        logo_path = ticker_info['logo_url']

                    if ticker_symbol in existing_tickers:
                        # Update existing ticker
                        existing = existing_tickers[ticker_symbol]

                        # Update fields
                        existing.name = ticker_info['name']
                        existing.country = ticker_info['country']
                        existing.exchange = ticker_info['exchange']
                        existing.is_sp_500 = ticker_info['is_sp_500']
                        existing.is_nasdap_100 = ticker_info['is_nasdap_100']
                        existing.is_nasdaq_composite = ticker_info['is_nasdaq_composite']
                        existing.is_dow_jones = ticker_info['is_dow_jones']
                        existing.is_russell_2000 = ticker_info['is_russell_2000']

                        # Update yf_symbol field if it exists in ticker_info
                        if 'yf_symbol' in ticker_info:
                            existing.yf_symbol = ticker_info['yf_symbol']

                        # Update new fields if they exist in ticker_info
                        if 'classify' in ticker_info:
                            existing.classify = ticker_info['classify']
                        if 'list_date' in ticker_info:
                            existing.list_date = ticker_info['list_date']
                        if 'delist_date' in ticker_info:
                            existing.delist_date = ticker_info['delist_date']

                        # Update logo field if available
                        if logo_path:
                            existing.logo = logo_path

                        batch_updated += 1
                    else:
                        # Create new AllTicker object and add to session
                        new_ticker_data = {
                            'ticker': ticker_symbol,
                            'name': ticker_info['name'],
                            'country': ticker_info['country'],
                            'exchange': ticker_info['exchange'],
                            'is_sp_500': ticker_info['is_sp_500'],
                            'is_nasdap_100': ticker_info['is_nasdap_100'],
                            'is_nasdaq_composite': ticker_info['is_nasdaq_composite'],
                            'is_dow_jones': ticker_info['is_dow_jones'],
                            'is_russell_2000': ticker_info['is_russell_2000'],
                            'logo': logo_path
                        }

                        # Add yf_symbol field if it exists in ticker_info
                        if 'yf_symbol' in ticker_info:
                            new_ticker_data['yf_symbol'] = ticker_info['yf_symbol']

                        # Add new fields if they exist in ticker_info
                        if 'classify' in ticker_info:
                            new_ticker_data['classify'] = ticker_info['classify']
                        if 'list_date' in ticker_info:
                            new_ticker_data['list_date'] = ticker_info['list_date']
                        if 'delist_date' in ticker_info:
                            new_ticker_data['delist_date'] = ticker_info['delist_date']

                        new_ticker = AllTicker(**new_ticker_data)
                        session.add(new_ticker)
                        # Add to existing_tickers to prevent duplicates in subsequent batches
                        existing_tickers[ticker_symbol] = new_ticker
                        batch_saved += 1

                # Commit each batch with error handling
                try:
                    session.commit()
                    total_saved += batch_saved
                    total_updated += batch_updated
                    self.batch_counter += 1
                    logger.info(f"Committed batch {self.batch_counter} with {len(batch)} tickers ({batch_saved} new, {batch_updated} updated)")
                except Exception as e:
                    logger.error(f"Error committing batch {self.batch_counter}: {str(e)}")
                    session.rollback()
                    # Try to process each ticker individually to identify the problematic one
                    self._process_batch_individually(session, batch, existing_tickers)

            # Update processed count
            self.processed_count = total_saved + total_updated

        logger.info(f"Saved {total_saved} new tickers and updated {total_updated} existing tickers")
        logger.info(f"Processed {total_logos_processed} logos")

    def _process_batch_individually(self, session, batch, existing_tickers):
        """
        Process each ticker in a batch individually when batch commit fails.

        Args:
            session: SQLAlchemy session
            batch: List of ticker info dictionaries
            existing_tickers: Dictionary of existing tickers
        """
        logger.info(f"Processing {len(batch)} tickers individually due to batch commit failure")

        for ticker_info in batch:
            ticker_symbol = ticker_info['ticker']
            try:
                if ticker_symbol in existing_tickers:
                    # Update existing ticker
                    existing = existing_tickers[ticker_symbol]
                    existing.name = ticker_info['name']
                    existing.country = ticker_info['country']
                    existing.exchange = ticker_info['exchange']
                    existing.is_sp_500 = ticker_info['is_sp_500']
                    existing.is_nasdap_100 = ticker_info['is_nasdap_100']
                    existing.is_nasdaq_composite = ticker_info['is_nasdaq_composite']
                    existing.is_dow_jones = ticker_info['is_dow_jones']
                    existing.is_russell_2000 = ticker_info['is_russell_2000']

                    # Update yf_symbol field if it exists in ticker_info
                    if 'yf_symbol' in ticker_info:
                        existing.yf_symbol = ticker_info['yf_symbol']

                    # Update new fields if they exist in ticker_info
                    if 'classify' in ticker_info:
                        existing.classify = ticker_info['classify']
                    if 'list_date' in ticker_info:
                        existing.list_date = ticker_info['list_date']
                    if 'delist_date' in ticker_info:
                        existing.delist_date = ticker_info['delist_date']

                    logger.debug(f"Updated ticker {ticker_symbol} individually")
                else:
                    # Create new ticker
                    new_ticker_data = {
                        'ticker': ticker_symbol,
                        'name': ticker_info['name'],
                        'country': ticker_info['country'],
                        'exchange': ticker_info['exchange'],
                        'is_sp_500': ticker_info['is_sp_500'],
                        'is_nasdap_100': ticker_info['is_nasdap_100'],
                        'is_nasdaq_composite': ticker_info['is_nasdaq_composite'],
                        'is_dow_jones': ticker_info['is_dow_jones'],
                        'is_russell_2000': ticker_info['is_russell_2000']
                    }

                    # Add yf_symbol field if it exists in ticker_info
                    if 'yf_symbol' in ticker_info:
                        new_ticker_data['yf_symbol'] = ticker_info['yf_symbol']

                    # Add new fields if they exist in ticker_info
                    if 'classify' in ticker_info:
                        new_ticker_data['classify'] = ticker_info['classify']
                    if 'list_date' in ticker_info:
                        new_ticker_data['list_date'] = ticker_info['list_date']
                    if 'delist_date' in ticker_info:
                        new_ticker_data['delist_date'] = ticker_info['delist_date']

                    new_ticker = AllTicker(**new_ticker_data)
                    session.add(new_ticker)
                    existing_tickers[ticker_symbol] = new_ticker
                    logger.debug(f"Added new ticker {ticker_symbol} individually")

                # Commit each ticker individually
                session.commit()

            except Exception as e:
                logger.error(f"Failed to process ticker {ticker_symbol} individually: {str(e)}")
                session.rollback()
                continue

    def _update_prev_tickers(self, prev_tickers):
        """
        Update ticker symbols that have changed based on the previous ticker mapping.

        Args:
            prev_tickers: Dictionary mapping new ticker symbols to their previous symbols
        """
        if not prev_tickers:
            logger.info("No previous ticker mappings to process")
            return

        logger.info(f"Processing {len(prev_tickers)} ticker symbol changes")
        updated_count = 0
        batch_size = 100

        with get_db_session() as session:
            # Process ticker changes in batches
            batch = []
            for new_ticker, old_ticker in prev_tickers.items():
                # Find the old ticker in the database
                ticker_record = session.query(AllTicker).filter(AllTicker.ticker == new_ticker).first()

                if ticker_record:
                    # Update the ticker symbol
                    ticker_record.prev_ticker = old_ticker

                    logger.info(f"Updating ticker: {new_ticker}'s prev_ticker → {old_ticker}")
                    updated_count += 1

                    batch.append(old_ticker)

                    # Commit in batches
                    if len(batch) >= batch_size:
                        session.commit()
                        logger.info(f"Committed batch of {len(batch)} ticker changes")
                        batch = []
                else:
                    logger.info(f"New ticker {new_ticker} already exists, skipping update from {old_ticker}")

            # Commit any remaining changes
            if batch:
                session.commit()
                logger.info(f"Committed final batch of {len(batch)} ticker changes")

        logger.info(f"Updated {updated_count} tickers with new symbols")

    def _get_us_tickers_from_tushare(self):
        """
        Get US stock tickers from Tushare API.
        """
        try:
            import tushare as ts

            # Check if TUSHARE_TOKEN is set in environment variables
            tushare_token = os.getenv('TUSHARE_TOKEN')
            if not tushare_token:
                logger.error("TUSHARE_TOKEN environment variable is not set")
                raise ValueError("TUSHARE_TOKEN environment variable is not set")

            # Initialize Tushare with token
            pro = ts.pro_api(tushare_token)
            logger.info("Successfully initialized Tushare API")

            # Get all US stocks with pagination
            all_us_stocks = []
            offset = 0
            limit = 5000  # Maximum allowed per request

            while True:
                logger.info(f"Fetching US stocks batch: offset={offset}, limit={limit}")
                batch_stocks = pro.us_basic(offset=str(offset), limit=str(limit))

                if batch_stocks.empty:
                    logger.info("No more US stocks to fetch")
                    break

                all_us_stocks.append(batch_stocks)
                offset += limit

                # Add a small delay to be respectful to the API
                time.sleep(0.5)

                # Break if we got less than the limit (last batch)
                if len(batch_stocks) < limit:
                    break

            # Combine all batches
            if all_us_stocks:
                us_stocks = pd.concat(all_us_stocks, ignore_index=True)
            else:
                logger.warning("No US stocks retrieved from Tushare")
                return []

            logger.info(f"Retrieved {len(us_stocks)} US stocks from Tushare")

            # Process the data
            ticker_info_list = self._process_tushare_us_data(us_stocks)

            # Save tickers to database
            self._save_tickers_to_db(ticker_info_list)

            return ticker_info_list

        except ImportError:
            logger.error("Tushare package is not installed. Please install it with 'pip install tushare'")
            raise
        except Exception as e:
            logger.error(f"Error getting US tickers from Tushare: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    def _process_tushare_us_data(self, us_stocks_df: pd.DataFrame) -> List[Dict]:
        """
        Process Tushare US stocks data and convert to our format.

        Args:
            us_stocks_df: DataFrame containing US stocks data from Tushare

        Returns:
            List of dictionaries containing ticker information
        """
        logger.info(f"Processing {len(us_stocks_df)} US stocks from Tushare")

        # Get existing tickers with logos
        existing_logos = {}
        with get_db_session() as session:
            for ticker_obj in session.query(AllTicker.ticker, AllTicker.logo).all():
                if ticker_obj.logo:  # If logo exists and is not None/empty
                    existing_logos[ticker_obj.ticker] = ticker_obj.logo

        ticker_info_list = []

        for _, row in us_stocks_df.iterrows():
            try:
                # Extract data from Tushare format
                ts_code = row.get('ts_code', '')
                name = row.get('name', '') or row.get('enname', '')  # Prefer Chinese name, fallback to English
                enname = row.get('enname', '')
                classify = row.get('classify', '')
                list_date = row.get('list_date', '')
                delist_date = row.get('delist_date', '')

                # Skip if no ticker code
                if not ts_code or pd.isna(ts_code) or ts_code == 'None':
                    logger.warning(f"Skipping row with no valid ts_code: ts_code={ts_code}, enname={enname}")
                    continue

                # Skip delisted stocks
                if delist_date and delist_date != 'NaT' and pd.notna(delist_date):
                    logger.warning(f"Skipping delisted stock {ts_code} (delisted: {delist_date})")
                    continue

                # Convert ticker format (remove any suffix if needed)
                ticker_symbol = ts_code

                # Use English name if Chinese name is not available or is None
                company_name = name if name and name != 'None' else enname
                if not company_name or company_name == 'None':
                    logger.warning(f"No company name found for ticker {ticker_symbol}, skipping")
                    continue

                # Determine exchange using the exchange detector
                # exchange = detect_exchange(ticker_symbol, classify)
                exchange = ''

                logo_url = get_logo_url(ticker_symbol, company_name)
                needs_logo_update = True

                # For US stocks, symbol and yf_symbol are identical (no leading zeros to strip)
                stripped_symbol = strip_leading_zeros(ticker_symbol)
                yf_symbol = generate_yf_symbol(stripped_symbol, 'US')

                ticker_info = {
                    'ticker': stripped_symbol,  # Store symbol without leading zeros
                    'yf_symbol': yf_symbol,     # Store Yahoo Finance compatible symbol
                    'name': company_name,
                    'country': 'US',
                    'exchange': exchange,
                    'classify': classify,
                    'list_date': list_date,
                    'delist_date': delist_date,
                    'is_sp_500': False,  # Will be updated later if needed
                    'is_nasdap_100': False,
                    'is_nasdaq_composite': False,
                    'is_dow_jones': False,
                    'is_russell_2000': False,
                    'logo_url': logo_url,
                    'needs_logo_update': needs_logo_update
                }

                ticker_info_list.append(ticker_info)

            except Exception as e:
                logger.error(f"Error processing ticker row {row}: {str(e)}")
                continue

        # Count tickers that need logo updates
        logo_update_count = sum(1 for ticker_info in ticker_info_list if ticker_info.get('needs_logo_update', False))

        logger.info(f"Successfully processed {len(ticker_info_list)} US tickers from Tushare")
        logger.info(f"Found {len(existing_logos)} tickers with existing logos")
        logger.info(f"Need to update logos for {logo_update_count} tickers")

        return ticker_info_list

